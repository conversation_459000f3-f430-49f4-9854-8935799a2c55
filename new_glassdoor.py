import asyncio
import json
import logging
import os
import re
import time
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, ProcessPoolExecutor, as_completed
from contextlib import contextmanager
from dataclasses import dataclass, field, asdict
from datetime import datetime
from itertools import cycle
from typing import Dict, List, Optional, Union, Any
from urllib.parse import urljoin, urlparse

import uvicorn
from bs4 import BeautifulSoup, NavigableString
from fastapi import FastAPI, Query, HTTPException, Body
from fastapi.responses import JSONResponse
from pydantic import BaseModel, field_validator
from selenium import webdriver
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException, WebDriverException,
    ElementClickInterceptedException, StaleElementReferenceException
)
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from fastapi.middleware.cors import CORSMiddleware
from selenium.webdriver.common.action_chains import ActionChains
import random
from math import ceil
import signal

# Enhanced anti-detection imports
try:
    import undetected_chromedriver as uc
    UNDETECTED_AVAILABLE = True
except ImportError:
    UNDETECTED_AVAILABLE = False
    print("Warning: undetected-chromedriver not available. Install with: pip install undetected-chromedriver")

try:
    from selenium_stealth import stealth
    STEALTH_AVAILABLE = True
except ImportError:
    STEALTH_AVAILABLE = False
    print("Warning: selenium-stealth not available. Install with: pip install selenium-stealth")

try:
    from seleniumbase import Driver
    SELENIUMBASE_AVAILABLE = True
except ImportError:
    SELENIUMBASE_AVAILABLE = False
    print("Warning: seleniumbase not available. Install with: pip install seleniumbase")

# Configuration Management
@dataclass
class ScraperConfig:
    """Configuration class for the scraper"""
    chrome_driver_path: str = '/Users/<USER>/Desktop/job_portal/chromedriver'
    max_workers: int = 3
    default_timeout: int = 20
    page_load_timeout: int = 30
    max_retries: int = 3
    retry_delay: int = 2

    # Anti-detection configuration
    driver_mode: str = 'undetected'  # Options: 'undetected', 'stealth', 'seleniumbase', 'standard'
    use_proxy: bool = False
    proxy_list: List[str] = field(default_factory=list)
    headless: bool = False  # Changed default to False for better detection avoidance
    enable_canvas_fingerprinting: bool = True
    enable_webgl_fingerprinting: bool = True
    randomize_viewport: bool = True

    # Performance configuration
    typing_speed: str = 'very_fast'  # Options: 'very_fast', 'fast', 'normal', 'slow', 'ultra_fast'
    use_ultra_fast_forms: bool = True  # Use instant form filling for better performance

    # Cloudflare optimization settings
    cloudflare_timeout: int = 15  # Reduced from 45 seconds
    cloudflare_max_retries: int = 3  # Maximum retry attempts
    progressive_timeout: bool = True  # Use progressive timeout reduction
    fast_challenge_detection: bool = True  # Use optimized challenge detection
    auto_fallback_drivers: bool = True  # Auto-fallback to other driver modes
    challenge_detection_interval: float = 0.5  # Check every 0.5 seconds instead of 2
    
    # Selectors with fallbacks
    selectors: Dict[str, List[str]] = field(default_factory=lambda: {
        'title': [
            "h1[id^='jd-job-title-']",
            "h1[data-test='job-title']",
            ".JobDetails_jobTitle__Nw_N2",
            "h1.css-1qaijid",
            "h1"
        ],
        'company': [
            "h4.EmployerProfile_employerNameHeading__bXBYr",
            "[data-test='employer-name']",
            ".EmployerProfile_profileContainer__d6vLt h4",
            ".EmployerProfile_employerNameHeading__bXBYr h4",
            "h4"
        ],
        'location': [
            "div[data-test='location']",
            ".JobDetails_location__mSg5h",
            "[data-test='job-location']"
        ],
        'salary': [
            "div[data-test='detailSalary']",
            ".JobDetails_salary__6VyJK",
            "[data-test='salary']"
        ],
        'easy_apply': [
            "button[data-test='easyApply']",
            "button[data-test='apply-button']",
            "button.css-1n6j6mr"
        ],
        'company_logo': [
            ".EmployerProfile_profileContainer__63w3R img",
            ".EmployerProfile_logo__3xqON img",
            "img[alt*='logo']"
        ],
        'job_description': [
            "div.JobDetails_jobDescription__uW_fK",
            ".JobDetails_jobDescription__6VeBn",
            "[data-test='jobDescriptionContent']"
        ],
        'show_more': [
            "button[data-test='show-more-cta']",
            "button[data-test='show-more']",
            "button.css-1gpqj0y"
        ],
        'load_more': [
            "button[data-test='load-more']",
            "button[data-test='pagination-footer-next']",
            "button.css-1gpqj0y"
        ],
        'job_links': [
            "a.JobCard_jobTitle__GLyJ1[data-test='job-title']",
            "a[data-test='job-title']",
            ".JobCard_jobTitle__rw2J1 a"
        ]
    })
    
    user_agents: List[str] = field(default_factory=lambda: [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ])

# Pydantic Models for API
class JobPosting(BaseModel):
    title: str
    company_name: Optional[str] = None
    location: Optional[str] = None
    salary: Optional[str] = None
    job_type: Optional[str] = None
    pay: Optional[str] = None
    work_location: Optional[str] = None
    benefits: Optional[Union[str, List[str]]] = None
    schedule: Optional[Union[str, List[str]]] = None
    education: Optional[str] = None
    most_relevant_skills: Optional[List[str]] = field(default_factory=list)
    other_relevant_skills: Optional[List[str]] = field(default_factory=list)
    easy_apply: bool = False
    company_logo: Optional[str] = None
    job_description: Optional[str] = None
    extra_sections: Dict[str, Any] = field(default_factory=dict)
    job_id: Optional[str] = None
    jd_url: Optional[str] = None

    @field_validator('title')
    @classmethod
    def title_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Job title cannot be empty')
        return v.strip()

    @field_validator('salary', 'pay')
    @classmethod
    def normalize_currency(cls, v):
        if v:
            return re.sub(r'[^\d,.-₹$€£\s]', '', v).strip()
        return v

    @field_validator('most_relevant_skills', 'other_relevant_skills', mode='before')
    @classmethod
    def ensure_list(cls, v):
        if isinstance(v, str):
            return [skill.strip() for skill in v.split(',') if skill.strip()]
        return v or []

class ScrapeRequest(BaseModel):
    job_title: str
    location: str
    num_jobs: int = Query(5, ge=1, le=50)

class ScrapeResponse(BaseModel):
    scraped_jobs: List[JobPosting]
    metadata: Dict[str, Any]

# Enhanced Logging
class ScraperLogger:
    def __init__(self, name: str = "GlassdoorScraper"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def info(self, msg: str, **kwargs):
        self.logger.info(msg, extra=kwargs)
    
    def error(self, msg: str, **kwargs):
        self.logger.error(msg, extra=kwargs)
    
    def warning(self, msg: str, **kwargs):
        self.logger.warning(msg, extra=kwargs)
    
    def debug(self, msg: str, **kwargs):
        self.logger.debug(msg, extra=kwargs)

# Smart Retry Handler
class SmartRetryHandler:
    def __init__(self, logger: ScraperLogger):
        self.logger = logger
        self.retry_strategies = {
            'timeout': self._handle_timeout,
            'stale_element': self._handle_stale_element,
            'no_such_element': self._handle_no_such_element,
            'click_intercepted': self._handle_click_intercepted,
            'general': self._handle_general_error
        }
    
    def determine_retry_strategy(self, exception: Exception) -> str:
        if isinstance(exception, TimeoutException):
            return 'timeout'
        elif isinstance(exception, StaleElementReferenceException):
            return 'stale_element'
        elif isinstance(exception, NoSuchElementException):
            return 'no_such_element'
        elif isinstance(exception, ElementClickInterceptedException):
            return 'click_intercepted'
        return 'general'
    
    def _handle_timeout(self, delay: int) -> int:
        self.logger.warning(f"Timeout occurred, retrying with {delay * 2}s delay")
        return delay * 2
    
    def _handle_stale_element(self, delay: int) -> int:
        self.logger.warning("Stale element reference, refreshing page state")
        return delay
    
    def _handle_no_such_element(self, delay: int) -> int:
        self.logger.warning("Element not found, trying fallback selectors")
        return delay
    
    def _handle_click_intercepted(self, delay: int) -> int:
        self.logger.warning("Click intercepted, scrolling to element")
        return delay
    
    def _handle_general_error(self, delay: int) -> int:
        return delay * 2

def safe_execute_with_retry(func, retries: int = 3, delay: int = 2, logger: ScraperLogger = None):
    """Execute function with smart retry logic"""
    retry_handler = SmartRetryHandler(logger or ScraperLogger())
    
    for attempt in range(retries):
        try:
            return func()
        except Exception as e:
            if attempt == retries - 1:
                logger.error(f"Failed after {retries} attempts: {str(e)}")
                return None
            
            strategy = retry_handler.determine_retry_strategy(e)
            delay = retry_handler.retry_strategies[strategy](delay)
            logger.warning(f"Attempt {attempt + 1} failed: {str(e)}, retrying in {delay}s")
            time.sleep(delay)
    
    return None

# Enhanced Driver Manager with Multiple Anti-Detection Methods
class DriverManager:
    def __init__(self, config: ScraperConfig, logger: ScraperLogger):
        self.config = config
        self.logger = logger
        self.user_agent_cycle = cycle(config.user_agents)
        self.proxy_cycle = cycle(config.proxy_list) if config.proxy_list else None

    def create_driver(self) -> webdriver.Chrome:
        """Create driver based on configured mode with enhanced anti-detection"""
        mode = self.config.driver_mode.lower()

        if mode == 'undetected' and UNDETECTED_AVAILABLE:
            return self._create_undetected_driver()
        elif mode == 'seleniumbase' and SELENIUMBASE_AVAILABLE:
            return self._create_seleniumbase_driver()
        elif mode == 'stealth' and STEALTH_AVAILABLE:
            return self._create_stealth_driver()
        else:
            self.logger.warning(f"Mode '{mode}' not available or not supported, falling back to enhanced standard driver")
            return self._create_enhanced_standard_driver()

    def _create_undetected_driver(self):
        """Create undetected Chrome driver"""
        self.logger.info("Creating undetected Chrome driver")

        options = uc.ChromeOptions()

        # Basic stealth options
        if self.config.headless:
            options.add_argument('--headless=new')

        # Enhanced viewport randomization
        if self.config.randomize_viewport:
            width = random.randint(1200, 1920)
            height = random.randint(800, 1080)
            options.add_argument(f'--window-size={width},{height}')
        else:
            options.add_argument('--window-size=1366,768')

        # Proxy support
        if self.config.use_proxy and self.proxy_cycle:
            proxy = next(self.proxy_cycle)
            options.add_argument(f'--proxy-server={proxy}')
            self.logger.info(f"Using proxy: {proxy}")

        # Enhanced preferences for realism
        prefs = self._get_realistic_prefs()
        options.add_experimental_option("prefs", prefs)

        # Additional stealth arguments
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-gpu')
        options.add_argument('--remote-debugging-port=0')  # Disable remote debugging

        try:
            driver = uc.Chrome(
                options=options,
                use_subprocess=False,
                version_main=None  # Auto-detect Chrome version
            )

            # Apply additional stealth measures
            self._apply_advanced_stealth(driver)

            return driver

        except Exception as e:
            self.logger.error(f"Failed to create undetected driver: {e}")
            return self._create_enhanced_standard_driver()

    def _create_seleniumbase_driver(self):
        """Create SeleniumBase driver with UC mode"""
        self.logger.info("Creating SeleniumBase driver")

        try:
            # Enhanced viewport randomization
            if self.config.randomize_viewport:
                width = random.randint(1200, 1920)
                height = random.randint(800, 1080)
                window_size = f"{width},{height}"
            else:
                window_size = "1366,768"

            driver = Driver(
                uc=True,  # Enable undetected mode
                headless=self.config.headless,
                window_size=window_size,
                proxy=next(self.proxy_cycle) if self.config.use_proxy and self.proxy_cycle else None
            )

            # Apply additional stealth measures
            self._apply_advanced_stealth(driver)

            return driver

        except Exception as e:
            self.logger.error(f"Failed to create SeleniumBase driver: {e}")
            return self._create_enhanced_standard_driver()

    def _create_stealth_driver(self):
        """Create standard driver with selenium-stealth"""
        self.logger.info("Creating stealth-enhanced Chrome driver")

        options = Options()

        if self.config.headless:
            options.add_argument('--headless=new')

        # Enhanced stealth options
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-gpu')
        options.add_argument('--remote-debugging-port=0')

        # Enhanced viewport randomization
        if self.config.randomize_viewport:
            width = random.randint(1200, 1920)
            height = random.randint(800, 1080)
            options.add_argument(f'--window-size={width},{height}')
        else:
            options.add_argument('--window-size=1366,768')

        # Proxy support
        if self.config.use_proxy and self.proxy_cycle:
            proxy = next(self.proxy_cycle)
            options.add_argument(f'--proxy-server={proxy}')
            self.logger.info(f"Using proxy: {proxy}")

        # Enhanced preferences
        prefs = self._get_realistic_prefs()
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        # User agent rotation
        options.add_argument(f'--user-agent={next(self.user_agent_cycle)}')

        service = Service(self.config.chrome_driver_path)
        driver = webdriver.Chrome(service=service, options=options)

        # Apply selenium-stealth
        stealth(driver,
                languages=["en-US", "en"],
                vendor="Google Inc.",
                platform="Win32",
                webgl_vendor="Intel Inc.",
                renderer="Intel Iris OpenGL Engine",
                fix_hairline=True,
        )

        # Apply additional stealth measures
        self._apply_advanced_stealth(driver)

        return driver

    def _create_enhanced_standard_driver(self):
        """Create enhanced standard Chrome driver with maximum stealth"""
        self.logger.info("Creating enhanced standard Chrome driver")

        options = Options()

        if self.config.headless:
            options.add_argument('--headless=new')

        # Essential stealth options
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-gpu')
        options.add_argument('--remote-debugging-port=0')

        # Enhanced viewport randomization
        if self.config.randomize_viewport:
            width = random.randint(1200, 1920)
            height = random.randint(800, 1080)
            options.add_argument(f'--window-size={width},{height}')
        else:
            options.add_argument('--window-size=1366,768')

        # Proxy support
        if self.config.use_proxy and self.proxy_cycle:
            proxy = next(self.proxy_cycle)
            options.add_argument(f'--proxy-server={proxy}')
            self.logger.info(f"Using proxy: {proxy}")

        # Enhanced anti-detection arguments
        options.add_argument('--disable-background-networking')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-translate')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-ipc-flooding-protection')

        # Logging
        options.add_argument('--log-level=3')
        options.add_argument('--silent')

        # User agent rotation
        options.add_argument(f'--user-agent={next(self.user_agent_cycle)}')

        # Enhanced experimental options
        options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        options.add_experimental_option('useAutomationExtension', False)

        # Realistic preferences
        prefs = self._get_realistic_prefs()
        options.add_experimental_option("prefs", prefs)

        service = Service(self.config.chrome_driver_path)
        driver = webdriver.Chrome(service=service, options=options)

        # Apply advanced stealth measures
        self._apply_advanced_stealth(driver)

        return driver

    def _get_realistic_prefs(self) -> dict:
        """Get realistic browser preferences"""
        return {
            "profile.default_content_setting_values": {
                "images": 1,  # Allow images (more realistic)
                "media_stream": 1,  # Allow media
                "stylesheets": 1,  # Allow CSS
                "javascript": 1,  # Allow JavaScript
                "notifications": 2,  # Block notifications
                "popups": 2,  # Block popups
                "geolocation": 2,  # Block location
                "media_stream_camera": 2,  # Block camera
                "media_stream_mic": 2,  # Block microphone
            },
            "profile.managed_default_content_settings": {
                "notifications": 2,
                "popups": 2,
                "geolocation": 2
            },
            # Add realistic browser preferences
            "profile.default_content_settings": {"popups": 0},
            "profile.content_settings": {"pattern_pairs": {}},
            "profile.password_manager_enabled": False,
            "credentials_enable_service": False,
            "profile.default_content_setting_values.automatic_downloads": 2,
            "profile.default_content_setting_values.mixed_script": 2,
            # Language and locale settings
            "intl.accept_languages": "en-US,en;q=0.9",
            "intl.charset_default": "UTF-8",
            # Privacy settings
            "profile.block_third_party_cookies": False,
            "profile.cookie_controls_mode": 0,
            # Performance settings
            "profile.default_content_setting_values.plugins": 1,
            "profile.content_settings.plugin_whitelist.adobe-flash-player": {"*": {"setting": 1}},
            # WebRTC settings for better fingerprinting
            "profile.content_settings.exceptions.media_stream_camera": {},
            "profile.content_settings.exceptions.media_stream_mic": {},
        }

    def _apply_advanced_stealth(self, driver):
        """Apply advanced stealth JavaScript to mask automation"""
        stealth_script = """
        // Enhanced anti-detection script

        // Remove webdriver property
        Object.defineProperty(navigator, 'webdriver', {get: () => undefined});

        // Mock plugins with more realistic data
        Object.defineProperty(navigator, 'plugins', {
            get: () => [
                {name: 'Chrome PDF Plugin', description: 'Portable Document Format', filename: 'internal-pdf-viewer'},
                {name: 'Chrome PDF Viewer', description: 'PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai'},
                {name: 'Native Client', description: 'Native Client', filename: 'internal-nacl-plugin'},
                {name: 'Chromium PDF Plugin', description: 'Portable Document Format', filename: 'internal-pdf-viewer'},
                {name: 'Microsoft Edge PDF Plugin', description: 'PDF Plugin', filename: 'pdf'}
            ]
        });

        // Mock languages with more realistic data
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en', 'en-GB']
        });

        // Enhanced chrome object mocking
        if (!window.chrome) {
            window.chrome = {};
        }

        window.chrome.runtime = {
            onConnect: undefined,
            onMessage: undefined
        };

        window.chrome.loadTimes = function() {
            return {
                commitLoadTime: Date.now() / 1000 - Math.random() * 100,
                finishDocumentLoadTime: Date.now() / 1000 - Math.random() * 50,
                finishLoadTime: Date.now() / 1000 - Math.random() * 10,
                firstPaintAfterLoadTime: 0,
                firstPaintTime: Date.now() / 1000 - Math.random() * 50,
                navigationType: 'Other',
                wasFetchedViaSpdy: false,
                wasNpnNegotiated: false
            };
        };

        window.chrome.csi = function() {
            return {
                onloadT: Date.now(),
                startE: Date.now() - Math.random() * 1000,
                tran: 15
            };
        };

        // Mock permissions API
        if (navigator.permissions && navigator.permissions.query) {
            const originalQuery = navigator.permissions.query;
            navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        }

        // Mock connection with realistic values
        Object.defineProperty(navigator, 'connection', {
            get: () => ({
                downlink: Math.random() * 10 + 5, // 5-15 Mbps
                effectiveType: '4g',
                rtt: Math.floor(Math.random() * 50) + 20, // 20-70ms
                saveData: false,
                type: 'wifi'
            })
        });

        // Mock hardware concurrency
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: () => Math.floor(Math.random() * 8) + 2 // 2-10 cores
        });

        // Mock device memory
        Object.defineProperty(navigator, 'deviceMemory', {
            get: () => [2, 4, 8, 16][Math.floor(Math.random() * 4)]
        });

        // Enhanced canvas fingerprinting protection
        if (window.HTMLCanvasElement) {
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            const originalToBlob = HTMLCanvasElement.prototype.toBlob;
            const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

            HTMLCanvasElement.prototype.toDataURL = function(type) {
                if (type === 'image/png' || type === 'image/jpeg') {
                    // Add slight noise to canvas data
                    const context = this.getContext('2d');
                    const imageData = context.getImageData(0, 0, this.width, this.height);
                    for (let i = 0; i < imageData.data.length; i += 4) {
                        imageData.data[i] += Math.floor(Math.random() * 3) - 1;
                        imageData.data[i + 1] += Math.floor(Math.random() * 3) - 1;
                        imageData.data[i + 2] += Math.floor(Math.random() * 3) - 1;
                    }
                    context.putImageData(imageData, 0, 0);
                }
                return originalToDataURL.apply(this, arguments);
            };

            CanvasRenderingContext2D.prototype.getImageData = function() {
                const imageData = originalGetImageData.apply(this, arguments);
                // Add slight noise
                for (let i = 0; i < imageData.data.length; i += 4) {
                    imageData.data[i] += Math.floor(Math.random() * 3) - 1;
                    imageData.data[i + 1] += Math.floor(Math.random() * 3) - 1;
                    imageData.data[i + 2] += Math.floor(Math.random() * 3) - 1;
                }
                return imageData;
            };
        }

        // WebGL fingerprinting protection
        if (window.WebGLRenderingContext) {
            const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
                    return 'Intel Inc.';
                }
                if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL
                    return 'Intel Iris OpenGL Engine';
                }
                return originalGetParameter.apply(this, arguments);
            };
        }

        // Mock screen properties with slight randomization
        Object.defineProperty(screen, 'availHeight', {
            get: () => screen.height - Math.floor(Math.random() * 100)
        });

        Object.defineProperty(screen, 'availWidth', {
            get: () => screen.width - Math.floor(Math.random() * 100)
        });

        // Mock timezone
        try {
            Object.defineProperty(Intl.DateTimeFormat.prototype, 'resolvedOptions', {
                value: function() {
                    return {
                        locale: 'en-US',
                        calendar: 'gregory',
                        numberingSystem: 'latn',
                        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                        year: 'numeric',
                        month: 'numeric',
                        day: 'numeric'
                    };
                }
            });
        } catch (e) {}

        // Remove automation indicators
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_JSON;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy;

        // Mock battery API
        if (navigator.getBattery) {
            navigator.getBattery = () => Promise.resolve({
                charging: Math.random() > 0.5,
                chargingTime: Math.random() * 10000,
                dischargingTime: Math.random() * 50000,
                level: Math.random()
            });
        }

        console.log('Advanced stealth measures applied');
        """

        try:
            driver.execute_script(stealth_script)

            # Set realistic timeouts
            driver.set_page_load_timeout(self.config.page_load_timeout)
            driver.implicitly_wait(self.config.default_timeout)

            self.logger.info("Advanced stealth measures applied successfully")

        except Exception as e:
            self.logger.warning(f"Failed to apply some stealth measures: {e}")

    def create_cloudflare_resistant_driver(self, attempt=1):
        """Create a driver specifically optimized for Cloudflare bypass with intelligent fallback"""
        self.logger.info(f"Creating Cloudflare-resistant driver (attempt {attempt})")

        # Intelligent driver selection based on attempt number
        if attempt == 1:
            # First attempt: Try undetected (best success rate)
            if UNDETECTED_AVAILABLE:
                self.logger.info("Using undetected ChromeDriver (attempt 1)")
                return self._create_undetected_driver()
        elif attempt == 2:
            # Second attempt: Try SeleniumBase (good balance)
            if SELENIUMBASE_AVAILABLE:
                self.logger.info("Falling back to SeleniumBase (attempt 2)")
                return self._create_seleniumbase_driver()
        elif attempt == 3:
            # Third attempt: Try stealth (lightweight)
            if STEALTH_AVAILABLE:
                self.logger.info("Falling back to selenium-stealth (attempt 3)")
                return self._create_stealth_driver()

        # Fallback to enhanced standard driver
        self.logger.info(f"Using enhanced standard driver (attempt {attempt})")
        return self._create_enhanced_standard_driver()

    def get_next_driver_mode(self, current_attempt):
        """Get the next best driver mode for fallback"""
        driver_priority = [
            ('undetected', UNDETECTED_AVAILABLE),
            ('seleniumbase', SELENIUMBASE_AVAILABLE),
            ('stealth', STEALTH_AVAILABLE),
            ('standard', True)
        ]

        if current_attempt <= len(driver_priority):
            mode, available = driver_priority[current_attempt - 1]
            if available:
                return mode

        return 'standard'

# Enhanced Field Extractor
class FieldExtractor:
    def __init__(self, config: ScraperConfig, logger: ScraperLogger):
        self.config = config
        self.logger = logger
        self.section_map = {
            'responsibilities': 'responsibilities',
            'key responsibilities': 'responsibilities',
            'qualifications': 'qualifications',
            "what we're looking for": 'requirements',
            'what we are looking for': 'requirements',
            'requirements': 'requirements',
            "what you'll gain": 'perks',
            'what you will gain': 'perks',
            'benefits': 'benefits',
            'schedule': 'schedule',
            'job type': 'jobType',
            'type': 'jobType',
            'contract length': 'contractLength',
            'pay': 'pay',
            'stipend': 'pay',
            'work location': 'workLocation',
            'location': 'workLocation',
            'expected start date': 'expectedStartDate',
            'education': 'education',
            'most relevant skills': 'mostRelevantSkills',
            'other relevant skills': 'otherRelevantSkills',
            'time type': 'timeType',
            'job family group': 'jobFamilyGroup',
            'job family': 'jobFamily',
            'what experience is mandatory': 'mandatoryExperience',
            'what experience is beneficial (but optional)': 'beneficialExperience',
            'what we offer': 'perks',
            'application questions': 'applicationQuestions',
            'application deadline': 'applicationDeadline',
        }
    
    def safe_extract_text(self, driver: webdriver.Chrome, selectors: List[str], attribute: str = None) -> Optional[str]:
        """Extract text using fallback selectors"""
        for selector in selectors:
            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                if attribute:
                    result = element.get_attribute(attribute)
                else:
                    result = element.text.strip()
                
                if result:
                    return result
                    
            except NoSuchElementException:
                continue
            except Exception as e:
                self.logger.debug(f"Error with selector {selector}: {str(e)}")
                continue
        
        return None
    
    def extract_job_description_sections(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Optimized job description parsing with deduplication and better structure"""
        sections = {}
        desc_div = soup.find("div", class_="JobDetails_jobDescription__uW_fK")

        if not desc_div:
            # Try fallback selectors
            for selector in self.config.selectors['job_description']:
                desc_div = soup.select_one(selector.replace('div.', '').replace('div', ''))
                if desc_div:
                    break

        if not desc_div:
            return sections

        # Extract raw text for deduplication checking
        full_text = desc_div.get_text(separator=' ', strip=True)

        # Process structured content with deduplication
        sections = self._extract_structured_sections(desc_div, full_text)

        # Enhanced extraction for specific fields
        self._extract_enhanced_skills(sections, desc_div, full_text)
        self._extract_enhanced_education(sections, desc_div, full_text)
        self._extract_enhanced_location(sections, desc_div, full_text)

        # Clean and deduplicate sections
        sections = self._clean_and_deduplicate_sections(sections)

        return sections

    def _extract_structured_sections(self, desc_div, full_text: str) -> Dict[str, Any]:
        """Extract sections with better structure recognition and content parsing"""
        sections = {}
        seen_content = set()  # Track seen content to avoid duplicates

        # First, try to parse the structured content more intelligently
        self._parse_job_description_structure(desc_div, sections, seen_content)

        # Then find all bold/strong elements that likely indicate section headers
        headers = desc_div.find_all(['b', 'strong', 'h1', 'h2', 'h3', 'h4'])

        for header in headers:
            header_text = header.get_text(strip=True).lower().rstrip(':')

            # Skip if this is just repeating the job title
            if 'job designation' in header_text and any(title in header_text for title in ['data scientist', 'analyst', 'engineer']):
                continue

            # Map to standardized section names
            section_key = self.section_map.get(header_text, header_text)

            # Extract content following this header
            content = self._extract_content_after_header(header, seen_content)

            if content and section_key not in sections:
                sections[section_key] = content

        return sections

    def _parse_job_description_structure(self, desc_div, sections: dict, seen_content: set):
        """Parse the specific structure of Glassdoor job descriptions"""
        # Look for the main job description content
        main_content = []

        # Find all div elements that contain job description text
        content_divs = desc_div.find_all('div', recursive=True)

        for div in content_divs:
            # Skip if this div contains other divs (it's a container)
            if div.find('div'):
                continue

            text = div.get_text(strip=True)
            if text and len(text) > 10 and text not in seen_content:
                # Clean and format the text
                clean_text = self._clean_extracted_text(text)
                if clean_text and clean_text not in seen_content:
                    main_content.append(clean_text)
                    seen_content.add(clean_text)

        # Combine main content into job description
        if main_content:
            # Filter out very short items and combine related content
            filtered_content = [item for item in main_content if len(item) > 20]
            if filtered_content:
                # Join with proper formatting
                formatted_content = self._format_job_description(filtered_content)
                sections['job description'] = formatted_content

    def _extract_content_after_header(self, header, seen_content: set) -> list:
        """Extract content that follows a header element with better formatting"""
        content = []
        current = header.next_sibling

        # Look through siblings until we hit another header or end
        while current:
            if hasattr(current, 'name'):
                # Stop if we hit another header
                if current.name in ['b', 'strong', 'h1', 'h2', 'h3', 'h4']:
                    break

                # Extract text from various elements
                if current.name in ['div', 'p', 'li', 'ul', 'ol']:
                    if current.name in ['ul', 'ol']:
                        # Handle lists specially
                        for li in current.find_all('li'):
                            text = li.get_text(strip=True)
                            if text and text not in seen_content and len(text) > 3:
                                content.append(text)
                                seen_content.add(text)
                    else:
                        # Get text with better spacing
                        text = current.get_text(separator=' ', strip=True)
                        if text and text not in seen_content and len(text) > 3:
                            # Clean up the text
                            text = self._clean_extracted_text(text)
                            if text:
                                content.append(text)
                                seen_content.add(text)
            elif hasattr(current, 'string') and current.string:
                # Handle direct text nodes
                text = current.string.strip()
                if text and text not in seen_content and len(text) > 3:
                    content.append(text)
                    seen_content.add(text)

            current = current.next_sibling

        return content

    def _clean_extracted_text(self, text: str) -> str:
        """Clean and format extracted text"""
        import re

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        # Add proper spacing after periods, colons, etc.
        text = re.sub(r'([.!?:])([A-Z])', r'\1 \2', text)

        # Add spacing around brackets
        text = re.sub(r'\[([^\]]+)\]', r' [\1] ', text)

        # Clean up multiple spaces
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def _format_job_description(self, content_list: list) -> str:
        """Format job description content with proper structure"""
        import re

        # Join all content
        full_text = ' '.join(content_list)

        # Add proper spacing and structure
        # Add line breaks before section headers
        section_patterns = [
            r'(Required Education and experience:)',
            r'(Required knowledge, skills and abilities:)',
            r'(Job Location:)',
            r'(Qualification\(s\):)',
            r'(Responsibilities:)',
            r'(Requirements:)',
            r'(\[Plus\])'
        ]

        for pattern in section_patterns:
            full_text = re.sub(pattern, r'\n\n\1', full_text, flags=re.IGNORECASE)

        # Add spacing after colons
        full_text = re.sub(r':([A-Z])', r': \1', full_text)

        # Add spacing after periods followed by capital letters
        full_text = re.sub(r'\.([A-Z])', r'. \1', full_text)

        # Clean up multiple spaces and newlines
        full_text = re.sub(r'\s+', ' ', full_text)
        full_text = re.sub(r'\n\s*\n', '\n\n', full_text)

        return full_text.strip()

    def _extract_enhanced_skills(self, sections: Dict[str, Any], desc_div, full_text: str):
        """Enhanced skills extraction with proper categorization"""
        # Define skill categories
        technical_skills = set()
        soft_skills = set()

        # Technical skill keywords
        tech_keywords = {
            'programming': ['python', 'r', 'sql', 'java', 'javascript', 'c++', 'scala', 'matlab'],
            'data_tools': ['pandas', 'numpy', 'dplyr', 'tidyverse', 'spark', 'hadoop', 'kafka'],
            'ml_frameworks': ['tensorflow', 'pytorch', 'scikit-learn', 'keras', 'xgboost'],
            'databases': ['mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch'],
            'cloud': ['aws', 'azure', 'gcp', 'docker', 'kubernetes'],
            'analytics': ['tableau', 'power bi', 'looker', 'qlik'],
            'version_control': ['git', 'github', 'gitlab', 'svn'],
            'statistical': ['statistics', 'statistical analysis', 'survival analysis', 'regression']
        }

        # Soft skill keywords
        soft_keywords = [
            'communication', 'leadership', 'teamwork', 'collaboration', 'problem solving',
            'analytical thinking', 'creativity', 'adaptability', 'time management',
            'project management', 'mentoring', 'presentation skills'
        ]

        # Extract from existing sections
        skill_sections = ['required knowledge, skills and abilities', 'skills', 'requirements', 'qualifications']

        for section_name, content in sections.items():
            if any(skill_sec in section_name.lower() for skill_sec in skill_sections):
                if isinstance(content, list):
                    for item in content:
                        self._categorize_skill_text(item, technical_skills, soft_skills, tech_keywords, soft_keywords)
                else:
                    self._categorize_skill_text(str(content), technical_skills, soft_skills, tech_keywords, soft_keywords)

        # Also extract from full text using patterns
        self._extract_skills_from_patterns(full_text, technical_skills, soft_skills, tech_keywords, soft_keywords)

        # Update sections with categorized skills
        if technical_skills:
            sections['most_relevant_skills'] = sorted(list(technical_skills))
        if soft_skills:
            sections['other_relevant_skills'] = sorted(list(soft_skills))

    def _categorize_skill_text(self, text: str, technical_skills: set, soft_skills: set,
                              tech_keywords: dict, soft_keywords: list):
        """Categorize a piece of text into technical or soft skills"""
        text_lower = text.lower()

        # Check for technical skills
        for category, keywords in tech_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    # Extract the relevant part of the text
                    if len(text) < 100:  # Short text, likely a skill
                        technical_skills.add(text.strip())
                    else:  # Long text, extract the keyword context
                        technical_skills.add(keyword.title())

        # Check for soft skills
        for keyword in soft_keywords:
            if keyword in text_lower:
                if len(text) < 100:
                    soft_skills.add(text.strip())
                else:
                    soft_skills.add(keyword.title())

    def _extract_skills_from_patterns(self, text: str, technical_skills: set, soft_skills: set,
                                    tech_keywords: dict, soft_keywords: list):
        """Extract skills using regex patterns"""
        import re

        # Pattern for skills in lists or bullet points
        skill_patterns = [
            r'(?:proficiency|experience|knowledge).*?(?:in|with|of)\s+([^.]+)',
            r'(?:strong|good|excellent)\s+(?:knowledge|skills?)\s+(?:in|with|of)\s+([^.]+)',
            r'(?:ability to|capable of)\s+([^.]+)',
            r'(?:familiar with|familiarity with)\s+([^.]+)'
        ]

        for pattern in skill_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                skill_text = match.group(1).strip()
                if skill_text:
                    self._categorize_skill_text(skill_text, technical_skills, soft_skills,
                                              tech_keywords, soft_keywords)

    def _extract_enhanced_education(self, sections: Dict[str, Any], desc_div, full_text: str):
        """Enhanced education extraction with deduplication"""
        education_content = []
        seen_education = set()

        # Look for education in structured sections
        education_sections = ['required education and experience', 'education', 'qualifications']

        for section_name, content in sections.items():
            if any(edu_sec in section_name.lower() for edu_sec in education_sections):
                if isinstance(content, list):
                    for item in content:
                        if 'degree' in item.lower() or 'bachelor' in item.lower() or 'master' in item.lower():
                            clean_item = item.strip()
                            if clean_item not in seen_education:
                                education_content.append(clean_item)
                                seen_education.add(clean_item)
                else:
                    content_str = str(content)
                    if 'degree' in content_str.lower() or 'bachelor' in content_str.lower() or 'master' in content_str.lower():
                        clean_content = content_str.strip()
                        if clean_content not in seen_education:
                            education_content.append(clean_content)
                            seen_education.add(clean_content)

        # Extract from qualification section at the end
        qual_pattern = r'Qualification\(s\):\s*([^<]+)'
        import re
        qual_match = re.search(qual_pattern, full_text, re.IGNORECASE)
        if qual_match:
            qual_text = qual_match.group(1).strip()
            qual_items = [item.strip() for item in qual_text.split('\n') if item.strip()]
            for item in qual_items:
                if item not in seen_education:
                    education_content.append(item)
                    seen_education.add(item)

        if education_content:
            sections['education'] = education_content if len(education_content) > 1 else education_content[0]

    def _extract_enhanced_location(self, sections: Dict[str, Any], desc_div, full_text: str):
        """Enhanced location extraction with better cleaning"""
        # Look for job location in structured content
        location_patterns = [
            r'Job Location:\s*([^<\n\r]+?)(?:\s*Qualification|$)',
            r'Location:\s*([^<\n\r]+?)(?:\s*Qualification|$)',
            r'Work Location:\s*([^<\n\r]+?)(?:\s*Qualification|$)'
        ]

        import re
        for pattern in location_patterns:
            match = re.search(pattern, full_text, re.IGNORECASE)
            if match:
                location = match.group(1).strip()
                # Clean up the location text
                location = re.sub(r'\s+', ' ', location)  # Remove extra whitespace
                location = location.split('Qualification')[0].strip()  # Remove qualification part

                if location and len(location) > 1:
                    sections['work_location'] = location
                    break

    def _clean_and_deduplicate_sections(self, sections: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and deduplicate section content"""
        cleaned_sections = {}

        for section_name, content in sections.items():
            if isinstance(content, list):
                # Remove duplicates and empty items
                cleaned_content = []
                seen = set()
                for item in content:
                    item_clean = str(item).strip()
                    if item_clean and item_clean not in seen and len(item_clean) > 2:
                        cleaned_content.append(item_clean)
                        seen.add(item_clean)

                if cleaned_content:
                    cleaned_sections[section_name] = cleaned_content if len(cleaned_content) > 1 else cleaned_content[0]
            else:
                content_clean = str(content).strip()
                if content_clean and len(content_clean) > 2:
                    cleaned_sections[section_name] = content_clean

        return cleaned_sections
    
    def _process_section_content(self, content: List[str]) -> Union[str, List[str]]:
        """Process section content based on type"""
        if len(content) == 1:
            return content[0]
        elif len(content) > 1:
            # Check if it looks like a list
            if all(len(item) < 200 for item in content):
                return content
            else:
                return ' '.join(content)
        return ""
    
    def _extract_work_location_from_bold(self, sections: Dict[str, Any], desc_div):
        """Extract work location from bold tags like <b>Locations:</b>"""
        try:
            for b_tag in desc_div.find_all('b'):
                text = b_tag.get_text(strip=True).lower()
                if 'location' in text:
                    # Get the text after the bold tag
                    next_sibling = b_tag.next_sibling
                    if next_sibling and isinstance(next_sibling, NavigableString):
                        location_text = next_sibling.strip()
                        if location_text:
                            sections['work_location'] = location_text
                            break
                    
                    # Alternative: get parent text and extract location
                    parent_text = b_tag.parent.get_text(strip=True)
                    if '|' in parent_text:
                        # Extract location part after "Locations:"
                        location_match = re.search(r'locations?:\s*([^|]+(?:\|[^|]+)*)', parent_text, re.IGNORECASE)
                        if location_match:
                            sections['work_location'] = location_match.group(1).strip()
                            break
        except Exception as e:
            self.logger.debug(f"Error extracting work location from bold: {e}")
    
    def _extract_education_from_text(self, sections: Dict[str, Any], desc_div):
        """Extract education requirements from job description text"""
        try:
            desc_text = desc_div.get_text(separator=' ')
            
            # Look for education patterns
            education_patterns = [
                r"(master['']?s degree|phd|bachelor['']?s degree|education requirements?:?)([^.]+)",
                r"(degree in|studies in|background in)([^.]+)",
                r"(minimum|required|preferred).*?(degree|education|qualification)([^.]+)",
                r"(master['']?s|phd|bachelor['']?s).*?(computer science|data science|mathematics|statistics|engineering)([^.]+)"
            ]
            
            for pattern in education_patterns:
                match = re.search(pattern, desc_text, re.IGNORECASE)
                if match:
                    education_text = match.group(0).strip()
                    if len(education_text) > 10:  # Ensure it's substantial
                        sections['education'] = education_text
                        break
        except Exception as e:
            self.logger.debug(f"Error extracting education: {e}")
    
    def _extract_skills_from_sections(self, sections: Dict[str, Any], desc_div):
        """Extract skills from various sections in job description"""
        try:
            # Look for skills in "Key Competencies", "What You'll Bring", etc.
            skills_sections = ['key competencies', 'what you\'ll bring', 'requirements', 'qualifications', 'skills']
            
            for section_name in skills_sections:
                # Find section by text content
                for element in desc_div.find_all(['h2', 'h3', 'b', 'strong']):
                    if section_name in element.get_text(strip=True).lower():
                        # Extract skills from next sibling or parent
                        skills = self._extract_skills_from_element(element)
                        if skills:
                            if 'most relevant skills' not in sections:
                                sections['most_relevant_skills'] = skills
                            elif 'other relevant skills' not in sections:
                                sections['other_relevant_skills'] = skills
                            break
            
            # Also look for bullet points with skills
            if 'most_relevant_skills' not in sections:
                skills_from_bullets = self._extract_skills_from_bullets(desc_div)
                if skills_from_bullets:
                    sections['most_relevant_skills'] = skills_from_bullets
                    
        except Exception as e:
            self.logger.debug(f"Error extracting skills: {e}")
    
    def _extract_skills_from_element(self, element) -> List[str]:
        """Extract skills from a specific element and its siblings"""
        skills = []
        try:
            # Look for list items after the element
            next_sibling = element.next_sibling
            while next_sibling:
                if hasattr(next_sibling, 'name'):
                    if next_sibling.name in ['ul', 'ol']:
                        for li in next_sibling.find_all('li'):
                            skill_text = li.get_text(strip=True)
                            if skill_text and len(skill_text) > 3:
                                skills.append(skill_text)
                        break
                    elif next_sibling.name in ['p', 'div']:
                        text = next_sibling.get_text(strip=True)
                        if text and len(text) < 200:  # Likely a skill if short
                            skills.append(text)
                next_sibling = next_sibling.next_sibling
        except Exception as e:
            self.logger.debug(f"Error extracting skills from element: {e}")
        return skills
    
    def _extract_skills_from_bullets(self, desc_div) -> List[str]:
        """Extract skills from bullet points in the description"""
        skills = []
        try:
            for ul in desc_div.find_all(['ul', 'ol']):
                for li in ul.find_all('li'):
                    text = li.get_text(strip=True)
                    # Check if it looks like a skill (not too long, contains tech terms)
                    if (text and len(text) < 100 and 
                        any(keyword in text.lower() for keyword in 
                            ['python', 'sql', 'machine learning', 'data', 'analytics', 'cloud', 'aws', 'azure'])):
                        skills.append(text)
        except Exception as e:
            self.logger.debug(f"Error extracting skills from bullets: {e}")
        return skills

    def extract_with_regex_fallback(self, text: str, field: str) -> Optional[str]:
        """Regex-based extraction as fallback"""
        if not text:
            return None
            
        patterns = {
            'jobType': r'\b(full[- ]?time|part[- ]?time|contract|internship|temporary)\b',
            'pay': r'(?:Salary|Pay)[:\-]?\s*([₹$€£]?\s?[\d,\.]+(?:\s*(?:per|/)?\s*\w+)?)',
            'workLocation': r'Work location[:\-]?\s*([A-Za-z, \-/]+)',
            'benefits': r'Benefits[:\-]?\s*(.+)',
            'schedule': r'Schedule[:\-]?\s*(.+)',
            'education': r'Education[:\-]?\s*(.+)',
        }
        
        pattern = patterns.get(field)
        if pattern:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return None

    def fast_extract_text(self, driver: webdriver.Chrome, selectors: list) -> Optional[str]:
        """Optimized text extraction with XPath preference"""
        # Try XPath first (usually faster than CSS)
        for selector in selectors:
            try:
                if selector.startswith('//'):
                    element = driver.find_element(By.XPATH, selector)
                else:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                text = element.text.strip()
                if text:
                    return text
            except:
                continue
        return None

    def extract_all_at_once(self, driver):
        """Extract multiple fields in single DOM query"""
        script = """
        const data = {};
        // Title
        const title = document.querySelector("h1[id*='job-title'], h1[data-test='job-title'], h1");
        data.title = title ? title.textContent.trim() : null;
        // Company  
        const company = document.querySelector(".EmployerProfile_employerNameHeading__bXBYr, [data-test='employer-name']");
        data.company = company ? company.textContent.trim() : null;
        // Location
        const location = document.querySelector("[data-test='location'], .location");
        data.location = location ? location.textContent.trim() : null;
        // Salary
        const salary = document.querySelector("[data-test='detailSalary'], .salary");
        data.salary = salary ? salary.textContent.trim() : null;
        // Job description HTML
        const desc = document.querySelector(".JobDetails_jobDescription__uW_fK, [data-test='jobDescriptionContent']");
        data.job_desc_html = desc ? desc.outerHTML : null;
        return data;
        """
        return driver.execute_script(script)

    def extract_all_at_once_with_retry(self, driver, max_retries=2):
        """Optimized extraction with reduced retries and better performance"""
        # Single optimized JavaScript extraction
        script = """
        const data = {};

        // Quick readiness check
        if (document.readyState !== 'complete') {
            return null;
        }

        // Optimized selectors (most common first)
        const quickSelectors = {
            title: "h1[id*='job-title'], h1[data-test='job-title'], h1",
            company: ".EmployerProfile_employerNameHeading__bXBYr h4, [data-test='employer-name']",
            location: "[data-test='location'], .JobDetails_location__*",
            salary: "[data-test='detailSalary'], .JobDetails_salary__*"
        };

        // Fast extraction using querySelector (not querySelectorAll)
        for (const [field, selector] of Object.entries(quickSelectors)) {
            try {
                const el = document.querySelector(selector);
                if (el && el.textContent) {
                    const text = el.textContent.trim();
                    if (text && text.length > 0) {
                        data[field] = text;
                    }
                }
            } catch (e) {
                // Continue to next field
            }
        }

        // Job description - single selector for performance
        try {
            const desc = document.querySelector(".JobDetails_jobDescription__uW_fK");
            if (desc && desc.innerHTML) {
                data.job_desc_html = desc.outerHTML;
            }
        } catch (e) {
            // Fallback selector
            try {
                const desc = document.querySelector("[data-test='jobDescriptionContent']");
                if (desc && desc.innerHTML) {
                    data.job_desc_html = desc.outerHTML;
                }
            } catch (e2) {
                // No job description found
            }
        }

        return data;
        """

        for attempt in range(max_retries):
            try:
                data = driver.execute_script(script)
                if data and data.get('title'):
                    return data
            except Exception as e:
                self.logger.warning(f"Extraction attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(0.5)  # Shorter delay

        return {}

    def extract_missing_fields(self, driver, existing_data):
        """Extract any missing critical fields using alternative methods"""
        missing_fields = {}

        # Job type fallback
        if not existing_data.get('job_type'):
            job_type_script = """
                const els = document.querySelectorAll('[class*="type" i], [id*="type" i], [data-test*="type" i]');
                for (const el of els) {
                    const text = el.textContent.trim();
                    if (text && text.length < 100 && /full[- ]?time|part[- ]?time|contract|internship|temporary/i.test(text)) {
                        return text;
                    }
                }
                return null;
            """
            missing_fields['job_type'] = driver.execute_script(job_type_script)

        # Work location fallback
        if not existing_data.get('work_location'):
            work_location_script = """
                const els = document.querySelectorAll('[class*="location" i], [id*="location" i], [data-test*="location" i]');
                for (const el of els) {
                    const text = el.textContent.trim();
                    if (text && (text.includes(',') || /remote|hybrid|onsite|office/i.test(text))) {
                        return text;
                    }
                }
                return null;
            """
            missing_fields['work_location'] = driver.execute_script(work_location_script)

        # Benefits fallback - Enhanced to look for benefits section
        if not existing_data.get('benefits'):
            benefits_script = """
                // Look for benefits section
                const benefitsHeading = Array.from(document.querySelectorAll('h2, h3')).find(el => 
                    /benefit/i.test(el.textContent) && !/rating/i.test(el.textContent)
                );
                if (benefitsHeading) {
                    // Look for benefits list in the section
                    let section = benefitsHeading.parentElement;
                    while (section && section.tagName !== 'SECTION') {
                        section = section.parentElement;
                    }
                    if (section) {
                        const benefitsList = section.querySelector('ul, ol');
                        if (benefitsList) {
                            return Array.from(benefitsList.querySelectorAll('li')).map(li => li.textContent.trim()).filter(Boolean);
                        }
                    }
                }
                
                // Fallback: look for any elements with "benefit" in class or text
                const els = document.querySelectorAll('[class*="benefit" i], [id*="benefit" i]');
                for (const el of els) {
                    const text = el.textContent.trim();
                    if (text && text.length > 5 && text.length < 500) {
                        return text;
                    }
                }
                return null;
            """
            benefits = driver.execute_script(benefits_script)
            if benefits:
                missing_fields['benefits'] = benefits

        # Schedule fallback
        if not existing_data.get('schedule'):
            schedule_script = """
                const els = document.querySelectorAll('[class*="schedule" i], [id*="schedule" i]');
                for (const el of els) {
                    const text = el.textContent.trim();
                    if (text && text.length > 3 && text.length < 100) {
                        return text;
                    }
                }
                return null;
            """
            missing_fields['schedule'] = driver.execute_script(schedule_script)

        # Education fallback - Enhanced to look in job description
        if not existing_data.get('education'):
            education_script = """
                // Look for education in job description
                const descEl = document.querySelector('.JobDetails_jobDescription__uW_fK, [data-test="jobDescriptionContent"]');
                if (descEl) {
                    const text = descEl.textContent;
                    const educationMatch = text.match(/(master['']?s degree|phd|bachelor['']?s degree|education requirements?:?)([^.]+)/i);
                    if (educationMatch) {
                        return educationMatch[0].trim();
                    }
                }
                
                // Fallback: look for education elements
                const els = document.querySelectorAll('[class*="education" i], [id*="education" i]');
                for (const el of els) {
                    const text = el.textContent.trim();
                    if (text && text.length > 3 && text.length < 100) {
                        return text;
                    }
                }
                return null;
            """
            missing_fields['education'] = driver.execute_script(education_script)

        # Most relevant skills fallback - Enhanced
        if not existing_data.get('most_relevant_skills'):
            skills_script = """
                // Look for skills in job description sections
                const descEl = document.querySelector('.JobDetails_jobDescription__uW_fK, [data-test="jobDescriptionContent"]');
                if (descEl) {
                    const skillsSections = ['key competencies', 'what you\\'ll bring', 'requirements', 'qualifications', 'skills'];
                    
                    for (const sectionName of skillsSections) {
                        const section = Array.from(descEl.querySelectorAll('h2, h3, b, strong')).find(el => 
                            el.textContent.toLowerCase().includes(sectionName)
                        );
                        if (section) {
                            let next = section.nextElementSibling;
                            if (next && (next.tagName === 'UL' || next.tagName === 'OL')) {
                                return Array.from(next.querySelectorAll('li')).map(li => li.textContent.trim()).filter(Boolean);
                            }
                            if (next && next.textContent) {
                                return next.textContent.split(/,|\\n/).map(s => s.trim()).filter(Boolean);
                            }
                        }
                    }
                    
                    // Look for bullet points with tech keywords
                    const bullets = descEl.querySelectorAll('ul li, ol li');
                    const skills = [];
                    for (const bullet of bullets) {
                        const text = bullet.textContent.trim();
                        if (text && text.length < 100 && 
                            /python|sql|machine learning|data|analytics|cloud|aws|azure|java|javascript|react|node/i.test(text)) {
                            skills.push(text);
                        }
                    }
                    if (skills.length > 0) {
                        return skills;
                    }
                }
                return null;
            """
            skills = driver.execute_script(skills_script)
            if skills:
                missing_fields['most_relevant_skills'] = skills

        # Other relevant skills fallback - Enhanced
        if not existing_data.get('other_relevant_skills'):
            other_skills_script = """
                // Look for "other relevant skills" or similar sections
                const descEl = document.querySelector('.JobDetails_jobDescription__uW_fK, [data-test="jobDescriptionContent"]');
                if (descEl) {
                    const section = Array.from(descEl.querySelectorAll('h2, h3, b, strong')).find(el => 
                        /other relevant skills|additional skills|nice to have|preferred skills/i.test(el.textContent)
                    );
                    if (section) {
                        let next = section.nextElementSibling;
                        if (next && (next.tagName === 'UL' || next.tagName === 'OL')) {
                            return Array.from(next.querySelectorAll('li')).map(li => li.textContent.trim()).filter(Boolean);
                        }
                        if (next && next.textContent) {
                            return next.textContent.split(/,|\\n/).map(s => s.trim()).filter(Boolean);
                        }
                    }
                }
                return null;
            """
            other_skills = driver.execute_script(other_skills_script)
            if other_skills:
                missing_fields['other_relevant_skills'] = other_skills

        return {k: v for k, v in missing_fields.items() if v}

class HumanBehavior:
    def __init__(self, logger=None, config=None):
        self.logger = logger or ScraperLogger()
        self.config = config or ScraperConfig()
        self.mouse_positions = []  # Track mouse positions for realistic movement
        self.cloudflare_attempts = 0  # Track attempts for progressive timeout

    def simulate_human_actions(self, driver):
        """Enhanced human behavior simulation with more realistic patterns"""
        try:
            # Realistic mouse movements with momentum
            actions = ActionChains(driver)

            # Get current window size for realistic movements
            window_size = driver.get_window_size()
            max_x = window_size['width']
            max_y = window_size['height']

            # Simulate natural mouse movement patterns
            num_movements = random.randint(2, 5)
            for i in range(num_movements):
                # Create curved mouse movements (more human-like)
                if i == 0:
                    # First movement - random position
                    x = random.randint(-max_x//4, max_x//4)
                    y = random.randint(-max_y//4, max_y//4)
                else:
                    # Subsequent movements - influenced by previous position
                    prev_x, prev_y = self.mouse_positions[-1] if self.mouse_positions else (0, 0)
                    x = prev_x + random.randint(-100, 100)
                    y = prev_y + random.randint(-100, 100)

                    # Keep within reasonable bounds
                    x = max(-max_x//2, min(max_x//2, x))
                    y = max(-max_y//2, min(max_y//2, y))

                self.mouse_positions.append((x, y))

                # Move with realistic timing
                actions.move_by_offset(x, y)
                actions.pause(random.uniform(0.1, 0.4))

                # Occasionally pause longer (simulate reading/thinking)
                if random.random() < 0.3:
                    actions.pause(random.uniform(0.5, 1.5))

            actions.perform()

        except Exception as e:
            self.logger.debug(f"Mouse simulation failed: {e}")

        # Enhanced scrolling patterns
        try:
            self._simulate_realistic_scrolling(driver)
        except Exception as e:
            self.logger.debug(f"Scroll simulation failed: {e}")

        # Random pauses with varying patterns
        self._simulate_reading_behavior()

    def _simulate_realistic_scrolling(self, driver):
        """Simulate realistic scrolling patterns"""
        scroll_patterns = [
            'quick_scan',      # Quick scrolls to scan content
            'detailed_read',   # Slow scrolls with pauses
            'search_scroll',   # Variable speed scrolling
            'back_and_forth'   # Scroll down then back up
        ]

        pattern = random.choice(scroll_patterns)

        if pattern == 'quick_scan':
            # Quick scanning - multiple small scrolls
            for _ in range(random.randint(3, 6)):
                scroll_amount = random.randint(200, 400)
                driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(0.3, 0.8))

        elif pattern == 'detailed_read':
            # Detailed reading - slower scrolls with longer pauses
            for _ in range(random.randint(2, 4)):
                scroll_amount = random.randint(150, 300)
                driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(1.5, 3.0))

        elif pattern == 'search_scroll':
            # Variable speed scrolling
            for _ in range(random.randint(2, 5)):
                scroll_amount = random.randint(100, 500)
                driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(0.5, 2.0))

        elif pattern == 'back_and_forth':
            # Scroll down then back up (common human behavior)
            # Scroll down
            for _ in range(random.randint(2, 4)):
                scroll_amount = random.randint(200, 400)
                driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(0.8, 1.5))

            # Scroll back up
            for _ in range(random.randint(1, 2)):
                scroll_amount = random.randint(100, 300)
                driver.execute_script(f"window.scrollBy(0, -{scroll_amount});")
                time.sleep(random.uniform(0.5, 1.2))

    def _simulate_reading_behavior(self):
        """Simulate realistic reading/thinking pauses"""
        behavior_type = random.choice(['quick_reader', 'careful_reader', 'scanner'])

        if behavior_type == 'quick_reader':
            if random.random() < 0.3:
                time.sleep(random.uniform(0.5, 1.5))
        elif behavior_type == 'careful_reader':
            if random.random() < 0.7:
                time.sleep(random.uniform(1.0, 3.0))
        else:  # scanner
            if random.random() < 0.2:
                time.sleep(random.uniform(0.2, 0.8))

    def wait_for_cloudflare(self, driver, max_wait=15, attempt=1):
        """Optimized Cloudflare challenge detection and handling"""
        self.logger.info(f"Checking for Cloudflare challenge (attempt {attempt}, timeout: {max_wait}s)...")

        start_time = time.time()
        challenge_detected = False
        last_check_time = 0
        check_interval = 0.5  # Check every 0.5 seconds for faster detection

        # Pre-compile indicators for faster matching
        cf_text_indicators = {
            'checking your browser', 'cloudflare', 'please wait', 'ddos protection',
            'security check', 'ray id', 'just a moment', 'verifying you are human',
            'browser verification', 'challenge', 'cf-browser-verification',
            'cf-challenge', 'turnstile', 'captcha', 'checking if the site connection is secure'
        }

        cf_url_indicators = {'challenges.cloudflare.com', 'cf-challenge', 'cdn-cgi/challenge-platform'}

        while time.time() - start_time < max_wait:
            current_time = time.time()

            # Only check at intervals to reduce overhead
            if current_time - last_check_time < check_interval:
                time.sleep(0.1)
                continue

            last_check_time = current_time

            try:
                # Fast challenge detection using optimized checks
                is_cloudflare, is_cf_url = self._fast_cloudflare_detection(
                    driver, cf_text_indicators, cf_url_indicators
                )

                if not is_cloudflare and not is_cf_url:
                    if challenge_detected:
                        elapsed = time.time() - start_time
                        self.logger.info(f"Cloudflare challenge completed in {elapsed:.1f}s")
                    else:
                        self.logger.info("No Cloudflare challenge detected")
                    return True

                if not challenge_detected:
                    self.logger.info("Cloudflare challenge detected, waiting for completion...")
                    challenge_detected = True

                # Minimal human behavior simulation for speed
                if random.random() < 0.3:  # Reduced frequency
                    self._minimal_challenge_behavior(driver)

                # Quick CAPTCHA check
                if current_time - start_time > 3:  # Only after 3 seconds
                    self._quick_captcha_check(driver)

            except Exception as e:
                self.logger.debug(f"Error in Cloudflare check: {e}")
                time.sleep(0.2)

        if challenge_detected:
            self.logger.warning(f"Cloudflare challenge timeout after {max_wait}s")
        return not challenge_detected

    def _fast_cloudflare_detection(self, driver, text_indicators, url_indicators):
        """Optimized Cloudflare detection with minimal overhead"""
        try:
            # Check URL first (fastest)
            current_url = driver.current_url.lower()
            is_cf_url = any(indicator in current_url for indicator in url_indicators)

            if is_cf_url:
                return True, True

            # Quick title check
            title = driver.title.lower()
            if any(indicator in title for indicator in text_indicators):
                return True, False

            # Minimal page source check (only first 2000 chars for speed)
            try:
                page_source = driver.execute_script("return document.documentElement.outerHTML.substring(0, 2000).toLowerCase();")
                is_cloudflare = any(indicator in page_source for indicator in text_indicators)
                return is_cloudflare, False
            except:
                # Fallback to full page source if script fails
                page_source = driver.page_source[:2000].lower()
                is_cloudflare = any(indicator in page_source for indicator in text_indicators)
                return is_cloudflare, False

        except Exception as e:
            self.logger.debug(f"Fast detection error: {e}")
            return False, False

    def _minimal_challenge_behavior(self, driver):
        """Minimal human behavior simulation for speed"""
        try:
            behavior_choice = random.choice(['mouse_move', 'wait'])

            if behavior_choice == 'mouse_move':
                # Very small mouse movement
                actions = ActionChains(driver)
                x_offset = random.randint(-10, 10)
                y_offset = random.randint(-10, 10)
                actions.move_by_offset(x_offset, y_offset)
                actions.perform()

        except Exception:
            pass

    def _quick_captcha_check(self, driver):
        """Quick CAPTCHA detection and interaction"""
        try:
            # Only check most common CAPTCHA selectors
            quick_selectors = [
                'input[type="checkbox"]',
                '.cf-turnstile',
                '[data-sitekey]'
            ]

            for selector in quick_selectors:
                try:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed() and element.is_enabled():
                        self.logger.info(f"Found CAPTCHA: {selector}")
                        time.sleep(random.uniform(0.5, 1.0))
                        element.click()
                        time.sleep(random.uniform(1.0, 2.0))
                        return True
                except NoSuchElementException:
                    continue
                except Exception:
                    continue

        except Exception:
            pass
        return False

    def wait_for_cloudflare_with_retry(self, driver, max_attempts=3):
        """Intelligent Cloudflare bypass with progressive timeout and driver fallback"""
        self.cloudflare_attempts = 0

        for attempt in range(1, max_attempts + 1):
            self.cloudflare_attempts = attempt

            # Progressive timeout reduction: 15s -> 12s -> 8s
            if self.config.progressive_timeout:
                timeout = max(8, self.config.cloudflare_timeout - (attempt - 1) * 3)
            else:
                timeout = self.config.cloudflare_timeout

            self.logger.info(f"Cloudflare bypass attempt {attempt}/{max_attempts} (timeout: {timeout}s)")

            # Try current driver
            if self.wait_for_cloudflare(driver, timeout, attempt):
                self.logger.info(f"Cloudflare bypass successful on attempt {attempt}")
                return True

            # If failed and auto-fallback enabled, suggest driver change
            if attempt < max_attempts and self.config.auto_fallback_drivers:
                self.logger.warning(f"Attempt {attempt} failed, considering driver fallback...")

                # Add small delay before retry
                time.sleep(random.uniform(1, 3))

        self.logger.error(f"All {max_attempts} Cloudflare bypass attempts failed")
        return False

    def get_progressive_timeout(self, attempt):
        """Calculate progressive timeout based on attempt number"""
        base_timeout = self.config.cloudflare_timeout
        if self.config.progressive_timeout:
            # Progressive reduction: 15s -> 12s -> 8s -> 5s
            reduction = (attempt - 1) * 3
            return max(5, base_timeout - reduction)
        return base_timeout

    def _simulate_challenge_waiting_behavior(self, driver):
        """Simulate realistic behavior while waiting for Cloudflare challenge"""
        behavior_choice = random.choice(['mouse_move', 'small_scroll', 'wait', 'click_area'])

        try:
            if behavior_choice == 'mouse_move':
                # Small, natural mouse movements
                actions = ActionChains(driver)
                for _ in range(random.randint(1, 3)):
                    x_offset = random.randint(-20, 20)
                    y_offset = random.randint(-20, 20)
                    actions.move_by_offset(x_offset, y_offset)
                    actions.pause(random.uniform(0.2, 0.5))
                actions.perform()

            elif behavior_choice == 'small_scroll':
                # Very small scroll movements
                scroll_amount = random.randint(10, 50)
                direction = random.choice([1, -1])
                driver.execute_script(f"window.scrollBy(0, {scroll_amount * direction});")

            elif behavior_choice == 'click_area':
                # Click on empty area (simulate impatience)
                if random.random() < 0.1:  # Only occasionally
                    actions = ActionChains(driver)
                    actions.move_by_offset(random.randint(-100, 100), random.randint(-100, 100))
                    actions.click()
                    actions.perform()

        except Exception as e:
            self.logger.debug(f"Challenge waiting behavior failed: {e}")

    def _handle_captcha_if_present(self, driver):
        """Attempt to handle CAPTCHA challenges if present"""
        try:
            # Look for common CAPTCHA selectors
            captcha_selectors = [
                'input[type="checkbox"]',  # Turnstile checkbox
                '.cf-turnstile',
                '[data-sitekey]',
                '.captcha-container',
                '#cf-challenge-form input[type="checkbox"]'
            ]

            for selector in captcha_selectors:
                try:
                    captcha_element = driver.find_element(By.CSS_SELECTOR, selector)
                    if captcha_element.is_displayed() and captcha_element.is_enabled():
                        self.logger.info(f"Found CAPTCHA element: {selector}")

                        # Simulate human-like interaction with CAPTCHA
                        time.sleep(random.uniform(1, 3))  # Think before clicking

                        # Move mouse to element naturally
                        actions = ActionChains(driver)
                        actions.move_to_element(captcha_element)
                        actions.pause(random.uniform(0.5, 1.0))
                        actions.click()
                        actions.perform()

                        self.logger.info("Clicked CAPTCHA element")
                        time.sleep(random.uniform(2, 5))  # Wait for processing
                        break

                except NoSuchElementException:
                    continue
                except Exception as e:
                    self.logger.debug(f"Error handling CAPTCHA with selector {selector}: {e}")
                    continue

        except Exception as e:
            self.logger.debug(f"CAPTCHA handling failed: {e}")

    def simulate_typing(self, element, text, typing_speed='fast'):
        """Simulate human-like typing with optimized speed"""
        speeds = {
            'slow': (0.05, 0.12),      # Reduced from (0.1, 0.3)
            'normal': (0.02, 0.06),    # Reduced from (0.05, 0.15)
            'fast': (0.01, 0.03),      # Reduced from (0.02, 0.08)
            'very_fast': (0.005, 0.015) # New ultra-fast option
        }

        min_delay, max_delay = speeds.get(typing_speed, speeds['fast'])

        # For very short text, type even faster
        if len(text) <= 10:
            min_delay *= 0.5
            max_delay *= 0.5

        # Type in chunks for better performance while maintaining realism
        chunk_size = random.randint(3, 7)  # Type 3-7 characters at once

        i = 0
        while i < len(text):
            # Get chunk of characters
            chunk = text[i:i + chunk_size]
            element.send_keys(chunk)
            i += len(chunk)

            # Short delay between chunks
            if i < len(text):
                time.sleep(random.uniform(min_delay, max_delay))

            # Occasionally pause longer (simulate thinking) - reduced frequency
            if random.random() < 0.05:  # Reduced from 0.1 to 0.05
                time.sleep(random.uniform(0.1, 0.3))  # Reduced from (0.3, 0.8)

    def fast_form_fill(self, element, text):
        """Ultra-fast form filling with minimal delays for efficiency"""
        # Clear the field first
        element.clear()
        time.sleep(random.uniform(0.1, 0.2))

        # Type the entire text at once with minimal delay
        element.send_keys(text)

        # Very short pause to simulate human completion
        time.sleep(random.uniform(0.1, 0.3))

class JobDataExtractor:
    def __init__(self):
        self.patterns = {
            'salary_range': r'₹([\d,]+(?:\.\d+)?)[KkLl]?\s*[-–]\s*₹([\d,]+(?:\.\d+)?)[KkLl]?',
            'pay_amount': r'₹([\d,]+(?:\.\d+)?)',
            'job_types': r'Job Types?:\s*([^\n]+)',
            'schedule': r'Schedule:\s*(.*?)(?=\n\w+:|$)',
            'work_location': r'Work Location:\s*([^\n]+)',
            'expected_hours': r'Expected hours:\s*(\d+)\s*per\s*week',
            'experience_years': r'(\d+)\s*years?\s*of.*?experience',
            'education': r'Education(?: requirements)?:\s*([^\n]+)',
            'benefits': r'Benefits?:\s*(.*?)(?=\n\w+:|$)',
        }
    def extract_from_html(self, html_content):
        soup = BeautifulSoup(html_content, 'html.parser')
        job_data = {}
        description_div = soup.find('div', class_='JobDetails_jobDescription__uW_fK')
        if description_div:
            job_data['full_description'] = description_div.get_text(strip=True)
            job_data.update(self._extract_sections(description_div))
        return job_data
    def _extract_sections(self, description_div):
        sections = {}
        text = description_div.get_text()
        for field, pattern in self.patterns.items():
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                if field == 'salary_range':
                    sections['salary_min'] = match.group(1)
                    sections['salary_max'] = match.group(2)
                else:
                    sections[field] = match.group(1).strip()
        return sections
    def extract_with_regex(self, text):
        extracted = {}
        for field, pattern in self.patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            if matches:
                if field == 'salary_range' and len(matches[0]) == 2:
                    extracted['salary_min'] = matches[0][0]
                    extracted['salary_max'] = matches[0][1]
                else:
                    extracted[field] = matches[0] if isinstance(matches[0], str) else matches[0][0]
        return extracted
    def smart_extract(self, content, content_type='html'):
        if content_type == 'html':
            return self.extract_from_html(content)
        else:
            return self.extract_with_regex(content)

# Main Scraper Class
class GlassdoorScraper:
    def __init__(self, config: ScraperConfig = None):
        self.config = config or ScraperConfig()
        self.logger = ScraperLogger()
        self.driver_manager = DriverManager(self.config, self.logger)
        self.field_extractor = FieldExtractor(self.config, self.logger)
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_workers)
        self.human_behavior = HumanBehavior(self.logger, self.config)
        self.driver_attempt = 1  # Track driver attempts for intelligent fallback

        # Log the anti-detection configuration
        self.logger.info(f"Initialized scraper with driver mode: {self.config.driver_mode}")
        self.logger.info(f"Cloudflare timeout: {self.config.cloudflare_timeout}s, Progressive: {self.config.progressive_timeout}")
        if self.config.use_proxy:
            self.logger.info(f"Proxy enabled with {len(self.config.proxy_list)} proxies")

        # Validate dependencies based on driver mode
        self._validate_dependencies()

    def _validate_dependencies(self):
        """Validate that required dependencies are available for the selected driver mode"""
        mode = self.config.driver_mode.lower()

        if mode == 'undetected' and not UNDETECTED_AVAILABLE:
            self.logger.warning("Undetected ChromeDriver not available. Install with: pip install undetected-chromedriver")
            self.config.driver_mode = 'standard'

        elif mode == 'seleniumbase' and not SELENIUMBASE_AVAILABLE:
            self.logger.warning("SeleniumBase not available. Install with: pip install seleniumbase")
            self.config.driver_mode = 'standard'

        elif mode == 'stealth' and not STEALTH_AVAILABLE:
            self.logger.warning("Selenium-stealth not available. Install with: pip install selenium-stealth")
            self.config.driver_mode = 'standard'

    def scrape_jobs(self, job_title: str, location: str, num_jobs: int = 5) -> Dict[str, Any]:
        """Main scraping method with monitoring and parallel session support"""
        start_time = time.time()
        metadata = {
            'start_time': datetime.utcnow().isoformat(),
            'job_title': job_title,
            'location': location,
            'requested_jobs': num_jobs,
            'scraped_jobs': 0,
            'failed_jobs': 0,
            'errors': [],
            'failed_urls': []
        }
        
        max_driver_attempts = 3 if self.config.auto_fallback_drivers else 1

        for driver_attempt in range(1, max_driver_attempts + 1):
            driver = None
            try:
                # Create driver with intelligent fallback
                driver = self.driver_manager.create_cloudflare_resistant_driver(driver_attempt)
                self.driver_attempt = driver_attempt

                self._perform_search(driver, job_title, location)

                # Over-collect URLs (2x requested) using fast method
                job_urls = self._collect_job_urls_fast(driver, num_jobs * 2)
                self.logger.info(f"Found {len(job_urls)} job URLs")

                # Extract jobs, stop when enough valid jobs
                jobs = []
                for i, url in enumerate(job_urls):
                    if len(jobs) >= num_jobs:
                        break

                    self.logger.info(f"Processing job {i+1}/{len(job_urls)}: {url}")

                    # Try to extract job with retry logic
                    job = None
                    for attempt in range(2):  # Try twice
                        try:
                            job = self._extract_current_page_fast(driver, url)
                            if job and job.title:  # Valid job extracted
                                break
                            elif attempt == 0:  # First attempt failed, try detailed extraction
                                self.logger.info(f"Fast extraction failed for {url}, trying detailed extraction")
                                job = self._extract_current_page(driver, url)
                                if job and job.title:
                                    break
                        except Exception as e:
                            self.logger.warning(f"Attempt {attempt + 1} failed for {url}: {str(e)}")
                            if attempt == 1:  # Last attempt
                                metadata['failed_jobs'] += 1
                                metadata['failed_urls'].append(url)
                                metadata['errors'].append(f"Failed to extract {url}: {str(e)}")

                    if job and job.title:
                        # Coerce string fields if needed
                        if isinstance(job.education, list):
                            job.education = ' '.join([str(e) for e in job.education if e]).strip()
                        if isinstance(job.benefits, list):
                            job.benefits = ' '.join([str(e) for e in job.benefits if e]).strip()
                        if isinstance(job.schedule, list):
                            job.schedule = ' '.join([str(e) for e in job.schedule if e]).strip()
                        if isinstance(job.job_type, list):
                            job.job_type = ' '.join([str(e) for e in job.job_type if e]).strip()

                        jobs.append(job)
                        self.logger.info(f"Successfully extracted job: {job.title}")
                    else:
                        metadata['failed_jobs'] += 1
                        metadata['failed_urls'].append(url)
                        self.logger.warning(f"Failed to extract job from {url}")

                metadata.update({
                    'scraped_jobs': len(jobs),
                    'execution_time': time.time() - start_time,
                    'end_time': datetime.utcnow().isoformat(),
                    'success_rate': f"{len(jobs)}/{len(job_urls)} ({len(jobs)/len(job_urls)*100:.1f}%)" if job_urls else "0%"
                })

                self.logger.info(f"Successfully scraped {len(jobs)} jobs in {metadata['execution_time']:.2f}s (Success rate: {metadata['success_rate']})")

                return {
                    'scraped_jobs': [job.dict() for job in jobs],
                    'metadata': metadata
                }

            except Exception as e:
                self.logger.error(f"Driver attempt {driver_attempt} failed: {str(e)}")
                metadata['errors'].append(f"Attempt {driver_attempt}: {str(e)}")

                if driver_attempt == max_driver_attempts:
                    # Last attempt failed
                    metadata['execution_time'] = time.time() - start_time
                    metadata['end_time'] = datetime.utcnow().isoformat()
                    raise
                else:
                    # Try next driver mode
                    self.logger.info(f"Trying next driver mode (attempt {driver_attempt + 1})")

            finally:
                if driver:
                    try:
                        driver.quit()
                    except:
                        pass

        # If we get here, all attempts failed
        metadata['execution_time'] = time.time() - start_time
        metadata['end_time'] = datetime.utcnow().isoformat()
        raise Exception("All driver attempts failed")
    
    def _perform_search(self, driver: webdriver.Chrome, job_title: str, location: str):
        """Enhanced search with Cloudflare handling and better error handling."""
        self.logger.info(f"Searching for '{job_title}' in '{location}'")
        def search_action():
            # Strategy 1: Try main search page
            try:
                self.logger.info("Attempting main search page")
                driver.get("https://www.glassdoor.co.in/Job/index.htm")

                # Optimized Cloudflare challenge handling with retry
                if not self.human_behavior.wait_for_cloudflare_with_retry(driver, self.config.cloudflare_max_retries):
                    self.logger.warning("Cloudflare challenge handling completed with potential issues")

                # Reduced wait time for better performance
                time.sleep(random.uniform(1, 3))
                self.human_behavior.simulate_human_actions(driver)

                # Check if page loaded
                if "glassdoor" not in driver.current_url.lower():
                    raise Exception("Failed to load Glassdoor main page")
                # Try to find and fill search fields with human-like behavior
                try:
                    # Wait a bit before interacting
                    time.sleep(random.uniform(1, 3))

                    job_input = driver.find_element(By.ID, "searchBar-jobTitle")
                    location_input = driver.find_element(By.ID, "searchBar-location")

                    # Optimized form filling based on configuration
                    if self.config.use_ultra_fast_forms:
                        # Ultra-fast form filling for maximum efficiency
                        self.human_behavior.fast_form_fill(job_input, job_title)
                        time.sleep(random.uniform(0.3, 0.6))
                        self.human_behavior.fast_form_fill(location_input, location)
                    else:
                        # Configurable speed typing
                        job_input.clear()
                        time.sleep(random.uniform(0.3, 0.8))
                        self.human_behavior.simulate_typing(job_input, job_title, self.config.typing_speed)
                        time.sleep(random.uniform(0.5, 1.0))
                        location_input.clear()
                        time.sleep(random.uniform(0.3, 0.8))
                        self.human_behavior.simulate_typing(location_input, location, self.config.typing_speed)

                    # Wait before submitting (simulate thinking)
                    time.sleep(random.uniform(1, 3))

                    # Hit ENTER to trigger search (matches manual behavior)
                    location_input.send_keys(Keys.RETURN)

                    # Wait for search to process and handle any Cloudflare challenges
                    time.sleep(random.uniform(1, 3))
                    if not self.human_behavior.wait_for_cloudflare(driver, max_wait=self.config.cloudflare_timeout):
                        self.logger.warning("Cloudflare challenge detected during search, may affect results")
                    # Check if search worked
                    if "/Job/" in driver.current_url:
                        self.logger.info("Main search successful via ENTER")
                        return True
                    # Fallback: Try clicking the search button if ENTER didn't work
                    search_btn = driver.find_element(By.XPATH, "//button[@type='submit']")
                    driver.execute_script("arguments[0].click();", search_btn)
                    time.sleep(5)
                    if "/Job/" in driver.current_url:
                        self.logger.info("Main search successful via button click fallback")
                        return True
                except Exception as e:
                    self.logger.warning(f"Main search failed: {e}")
            except Exception as e:
                self.logger.warning(f"Main page load failed: {e}")
            # Strategy 2: Direct URL construction
            try:
                self.logger.info("Attempting direct URL construction")
                import urllib.parse
                encoded_title = urllib.parse.quote_plus(job_title)
                search_url = f"https://www.glassdoor.co.in/Job/jobs.htm?sc.keyword={encoded_title}"
                driver.get(search_url)
                time.sleep(8)  # Longer wait
                # Check if we have job listings
                job_elements = driver.find_elements(By.CSS_SELECTOR, "a[data-test='job-title'], .JobCard_jobTitle__GLyJ1")
                if job_elements:
                    self.logger.info("Direct URL search successful")
                    return True
            except Exception as e:
                self.logger.warning(f"Direct URL failed: {e}")
            # Strategy 3: Google redirect method
            try:
                self.logger.info("Attempting Google redirect method")
                google_search = f"site:glassdoor.co.in {job_title} jobs {location}"
                google_url = f"https://www.google.com/search?q={urllib.parse.quote_plus(google_search)}"
                driver.get(google_url)
                time.sleep(3)
                # Look for Glassdoor links
                glassdoor_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'glassdoor.co.in/Job')]")
                if glassdoor_links:
                    glassdoor_links[0].click()
                    time.sleep(5)
                    self.logger.info("Google redirect successful")
                    return True
            except Exception as e:
                self.logger.warning(f"Google redirect failed: {e}")
            # Strategy 4: Alternative domain
            try:
                self.logger.info("Attempting alternative domain")
                alt_url = f"https://www.glassdoor.com/Job/india-{job_title.lower().replace(' ', '-')}-jobs-SRCH_IL.0,5_IN115.htm"
                driver.get(alt_url)
                time.sleep(5)
                job_elements = driver.find_elements(By.CSS_SELECTOR, "a[data-test='job-title']")
                if job_elements:
                    self.logger.info("Alternative domain successful")
                    return True
            except Exception as e:
                self.logger.warning(f"Alternative domain failed: {e}")
            raise RuntimeError("All search strategies failed")
        # Execute with retries
        result = safe_execute_with_retry(
            search_action, 
            retries=3,
            delay=5,
            logger=self.logger
        )
        if not result:
            raise RuntimeError("Search failed after all retries")

    def _collect_job_urls_fast(self, driver: webdriver.Chrome, num_jobs: int) -> list:
        """Faster job URL collection using JavaScript with better error handling"""
        job_urls = set()
        
        # JavaScript to collect all URLs at once with multiple strategies
        collect_script = """
        const urls = new Set();
        
        // Strategy 1: Standard job card links
        const standardSelectors = [
            "a[data-test='job-title']",
            "a.JobCard_jobTitle__GLyJ1", 
            ".JobCard_jobTitle__rw2J1 a",
            "a[href*='/job-listing/']",
            "a[href*='jl=']",
            "[data-test='job-card'] a"
        ];
        
        standardSelectors.forEach(selector => {
            try {
                document.querySelectorAll(selector).forEach(el => {
                    const href = el.getAttribute('href');
                    if (href && (href.includes('/job-listing/') || href.includes('jl='))) {
                        urls.add(href.startsWith('http') ? href : 'https://www.glassdoor.co.in' + href);
                    }
                });
            } catch (e) {
                // Continue with next selector
            }
        });
        
        // Strategy 2: Look for any links containing job IDs
        const allLinks = document.querySelectorAll('a[href*="jl="]');
        allLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.includes('jl=')) {
                urls.add(href.startsWith('http') ? href : 'https://www.glassdoor.co.in' + href);
            }
        });
        
        // Strategy 3: Look for job listing URLs in any format
        const jobListingLinks = document.querySelectorAll('a[href*="/job-listing/"]');
        jobListingLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href) {
                urls.add(href.startsWith('http') ? href : 'https://www.glassdoor.co.in' + href);
            }
        });
        
        return Array.from(urls);
        """
        
        # JavaScript to handle popups and modals
        popup_handler_script = """
        // Close authentication modals
        const authSelectors = [
            'button[data-test="closeButton"]',
            '.CloseButton',
            'button[class*="close"]',
            'button[class*="Close"]',
            '[data-test="authModalContainerV2"] button',
            '.authModalContent button',
            'button[aria-label*="close"]',
            'button[aria-label*="Close"]'
        ];
        
        // Close overlay modals
        const overlaySelectors = [
            '[data-test="authModalContainerV2"]',
            '.authModalContent',
            '.modal-overlay',
            '.modal-backdrop',
            '[class*="modal"]',
            '[class*="popup"]',
            '[class*="overlay"]'
        ];
        
        // Try to close modals
        authSelectors.forEach(selector => {
            try {
                const closeBtn = document.querySelector(selector);
                if (closeBtn && closeBtn.offsetParent !== null) {
                    closeBtn.click();
                    return true;
                }
            } catch (e) {
                // Continue with next selector
            }
        });
        
        // Try to remove overlays
        overlaySelectors.forEach(selector => {
            try {
                const overlay = document.querySelector(selector);
                if (overlay && overlay.offsetParent !== null) {
                    overlay.remove();
                    return true;
                }
            } catch (e) {
                // Continue with next selector
            }
        });
        
        // Try to press Escape key
        document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
        
        return false;
        """
        
        max_scrolls = 5  # Reduced from 10
        for scroll in range(max_scrolls):
            try:
                # Handle any popups before scrolling
                self._handle_popups_and_modals(driver)
                
                # Quick scroll and collect
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(1)  # Reduced wait time
                
                # Handle popups again after scrolling
                self._handle_popups_and_modals(driver)
                
                # Collect URLs with JavaScript
                urls = driver.execute_script(collect_script)
                if urls:
                    job_urls.update(urls)
                    self.logger.debug(f"Found {len(urls)} URLs on scroll {scroll + 1}")
                
                if len(job_urls) >= num_jobs:
                    break
                
                # Try load more (but don't wait long)
                try:
                    load_more_selectors = [
                        "button[data-test='load-more']",
                        "button[data-test='pagination-footer-next']",
                        "button.css-1gpqj0y",
                        "button[class*='load-more']",
                        "button[class*='pagination']"
                    ]
                    
                    for selector in load_more_selectors:
                        try:
                            load_more = driver.find_element(By.CSS_SELECTOR, selector)
                            if load_more.is_displayed():
                                # Handle popups before clicking
                                self._handle_popups_and_modals(driver)
                                
                                driver.execute_script("arguments[0].click();", load_more)
                                time.sleep(1)  # Minimal wait
                                
                                # Handle popups after clicking
                                self._handle_popups_and_modals(driver)
                                break
                        except:
                            continue
                except Exception as e:
                    self.logger.debug(f"Load more failed on scroll {scroll + 1}: {e}")
                    
            except Exception as e:
                self.logger.warning(f"Error during URL collection on scroll {scroll + 1}: {e}")
                continue
        
        # Clean and validate URLs
        valid_urls = []
        for url in job_urls:
            try:
                # Basic URL validation
                if url and ('glassdoor' in url.lower() or 'jl=' in url):
                    # Remove any fragments or unnecessary parameters
                    clean_url = url.split('#')[0].split('?')[0]
                    if 'jl=' in url:
                        # Keep the job ID parameter
                        clean_url = url.split('#')[0]
                    valid_urls.append(clean_url)
            except Exception as e:
                self.logger.debug(f"Invalid URL {url}: {e}")
        
        # Remove duplicates while preserving order
        seen = set()
        unique_urls = []
        for url in valid_urls:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)
        
        self.logger.info(f"Collected {len(unique_urls)} unique job URLs")
        return unique_urls[:num_jobs]

    def _extract_jobs_concurrent(self, job_urls: List[str]) -> List[JobPosting]:
        """Extract job details for each job URL in the same browser session (session persistence)."""
        driver = self.driver_manager.create_driver()
        jobs = []
        try:
            for i, url in enumerate(job_urls):
                if i > 0:
                    # Simulate human-like delay and actions
                    time.sleep(random.uniform(3, 7))
                    self.human_behavior.simulate_human_actions(driver)
                job = self._extract_current_page(driver, url)
                if job:
                    jobs.append(job)
        finally:
            driver.quit()
        return jobs

    def _handle_popups_and_modals(self, driver: webdriver.Chrome):
        """Comprehensive popup and modal handling"""
        try:
            # JavaScript to handle various types of popups and modals
            popup_script = """
            // Function to handle all types of popups and modals
            function handlePopups() {
                let closed = false;
                
                // 1. Close authentication modals (Glassdoor specific)
                const authSelectors = [
                    'button[data-test="closeButton"]',
                    '.CloseButton',
                    'button[class*="close"]',
                    'button[class*="Close"]',
                    '[data-test="authModalContainerV2"] button',
                    '.authModalContent button',
                    'button[aria-label*="close"]',
                    'button[aria-label*="Close"]',
                    '.closeButtonWrapper button',
                    'button[type="button"] svg[class*="CloseIcon"]',
                    'button svg[class*="CloseIcon"]'
                ];
                
                authSelectors.forEach(selector => {
                    try {
                        const closeBtn = document.querySelector(selector);
                        if (closeBtn && closeBtn.offsetParent !== null && closeBtn.style.display !== 'none') {
                            closeBtn.click();
                            closed = true;
                            console.log('Closed auth modal with selector:', selector);
                        }
                    } catch (e) {
                        // Continue with next selector
                    }
                });
                
                // 2. Remove modal overlays
                const overlaySelectors = [
                    '[data-test="authModalContainerV2"]',
                    '.authModalContent',
                    '.modal-overlay',
                    '.modal-backdrop',
                    '[class*="modal"]',
                    '[class*="popup"]',
                    '[class*="overlay"]',
                    '.ContentAndBottomSection'
                ];
                
                overlaySelectors.forEach(selector => {
                    try {
                        const overlay = document.querySelector(selector);
                        if (overlay && overlay.offsetParent !== null) {
                            overlay.remove();
                            closed = true;
                            console.log('Removed overlay with selector:', selector);
                        }
                    } catch (e) {
                        // Continue with next selector
                    }
                });
                
                // 3. Try to press Escape key
                document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
                
                // 4. Remove any fixed positioned elements that might be blocking
                const fixedElements = document.querySelectorAll('[style*="position: fixed"]');
                fixedElements.forEach(el => {
                    try {
                        const style = window.getComputedStyle(el);
                        if (style.zIndex > 1000) { // High z-index elements are usually modals
                            el.remove();
                            closed = true;
                            console.log('Removed fixed element with high z-index');
                        }
                    } catch (e) {
                        // Continue with next element
                    }
                });
                
                // 5. Enable scrolling if disabled
                document.body.style.overflow = 'auto';
                document.documentElement.style.overflow = 'auto';
                
                return closed;
            }
            
            return handlePopups();
            """
            
            # Execute popup handling
            popup_closed = driver.execute_script(popup_script)
            
            if popup_closed:
                self.logger.info("Successfully handled popup/modal")
                time.sleep(0.5)  # Brief pause after closing popup
                
        except Exception as e:
            self.logger.debug(f"Error handling popups: {e}")
    
    def _expand_show_more_aggressive(self, driver):
        """More aggressive show more expansion with popup handling"""
        show_more_selectors = [
            "button[data-test='show-more-cta']",
            "button[data-test='show-more']", 
            "button.css-1gpqj0y",
            "[class*='show-more']",
            "button:contains('Show more')",
            "button:contains('Read more')"
        ]
        
        for selector in show_more_selectors:
            try:
                if selector.startswith("button:contains"):
                    # XPath for text content
                    text_content = selector.split('(')[1].split(')')[0].strip("'")
                    xpath = f"//button[contains(text(), '{text_content}')]"
                    show_more = driver.find_element(By.XPATH, xpath)
                else:
                    show_more = driver.find_element(By.CSS_SELECTOR, selector)
                    
                if show_more.is_displayed() and show_more.is_enabled():
                    # Handle popups before clicking
                    self._handle_popups_and_modals(driver)
                    
                    driver.execute_script("arguments[0].click();", show_more)
                    time.sleep(1)
                    
                    # Handle popups after clicking
                    self._handle_popups_and_modals(driver)
                    break
            except Exception:
                continue

    def _expand_show_more(self, driver):
        """Click the 'Show more' button if present to expand the full job description."""
        self._expand_show_more_aggressive(driver)

    def _extract_company_logo(self, driver):
        """Extract company logo URL from the header section."""
        selectors = [
            ".EmployerProfile_profileContainer__63w3R img",
            ".EmployerProfile_logo__3xqON img",
            "img[alt*='logo']",
            "img.avatar-base_Image__2RcF9"
        ]
        for selector in selectors:
            try:
                img = driver.find_element(By.CSS_SELECTOR, selector)
                src = img.get_attribute("src")
                if src and src.startswith("http"):
                    return src
            except Exception:
                continue
        return None

    def _extract_current_page_fast(self, driver, job_url: str) -> Optional[JobPosting]:
        """Optimized extraction with reduced waits and comprehensive error handling"""
        try:
            driver.get(job_url)
            
            # Handle any popups that might appear when loading the page
            self._handle_popups_and_modals(driver)
            
            # Wait for page to load with multiple fallback strategies
            page_loaded = False
            try:
                # Strategy 1: Wait for title element
                WebDriverWait(driver, 8).until(
                    EC.any_of(
                        EC.presence_of_element_located((By.TAG_NAME, "h1")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, "[data-test='job-title']")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".JobDetails_jobDetailsHeader__Hd9M3")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, "[class*='job-title']"))
                    )
                )
                page_loaded = True
            except TimeoutException:
                self.logger.warning(f"Timeout waiting for page elements on {job_url}")
                
                # Strategy 2: Check if page has any content
                try:
                    page_text = driver.find_element(By.TAG_NAME, "body").text
                    if len(page_text) > 100:  # Page has some content
                        page_loaded = True
                        self.logger.info(f"Page loaded with fallback check for {job_url}")
                except:
                    pass
            
            if not page_loaded:
                self.logger.error(f"Page failed to load for {job_url}")
                return None
            
            # Handle popups again after page load
            self._handle_popups_and_modals(driver)
            
            # Aggressive show more expansion
            self._expand_show_more_aggressive(driver)
            
            # Handle popups after expanding content
            self._handle_popups_and_modals(driver)
            
            # Extract all data at once with JavaScript (with retry)
            data = self.field_extractor.extract_all_at_once_with_retry(driver)
            
            # Fallback extraction if main extraction fails
            if not data or not data.get('title'):
                self.logger.warning(f"Main extraction failed for {job_url}, trying fallback")
                data = self._fallback_extraction(driver)
            
            if not data or not data.get('title'):
                self.logger.error(f"All extraction methods failed for {job_url}")
                return None
            
            # Field-specific fallback extraction for missing fields
            missing_fields = self.field_extractor.extract_missing_fields(driver, data)
            data.update(missing_fields)
            
            # Process job description with enhanced parsing
            job_desc = None
            extra_sections = {}
            if data.get('job_desc_html'):
                try:
                    # Use enhanced parsing logic
                    soup = BeautifulSoup(data['job_desc_html'], 'html.parser')
                    extra_sections = self.field_extractor.extract_job_description_sections(soup)

                    # Generate clean markdown with better formatting
                    if extra_sections:
                        job_desc = self._generate_clean_markdown(extra_sections)
                except Exception as e:
                    self.logger.debug(f"Job description parsing failed: {e}")
                    # Fallback to simple text extraction
                    try:
                        soup = BeautifulSoup(data['job_desc_html'], 'html.parser')
                        job_desc = soup.get_text(separator='\n', strip=True)
                    except:
                        pass
            
            # Quick easy apply check with multiple selectors
            easy_apply = False
            try:
                easy_apply_selectors = [
                    "button[data-test='easyApply']",
                    "button[data-test='apply-button']",
                    "button[data-test='applyButton']",
                    "button.css-1n6j6mr",
                    ".JobDetails_applyButtonContainer__L36Bs button"
                ]
                for selector in easy_apply_selectors:
                    try:
                        easy_apply_btn = driver.find_element(By.CSS_SELECTOR, selector)
                        if easy_apply_btn.is_displayed():
                            easy_apply = True
                            break
                    except:
                        continue
            except Exception as e:
                self.logger.debug(f"Easy apply check failed: {e}")
            
            # Create JobPosting with comprehensive field mapping
            return JobPosting(
                title=data.get('title', ''),
                company_name=data.get('company'),
                location=data.get('location'),
                salary=data.get('salary'),
                job_type=data.get('job_type'),
                work_location=data.get('work_location'),
                benefits=data.get('benefits'),
                schedule=data.get('schedule'),
                education=data.get('education'),
                most_relevant_skills=data.get('most_relevant_skills', []),
                other_relevant_skills=data.get('other_relevant_skills', []),
                easy_apply=easy_apply,
                job_description=job_desc,
                extra_sections=extra_sections,
                job_id=self._extract_job_id(job_url),
                jd_url=job_url,
            )
        except Exception as e:
            self.logger.error(f"Fast extraction failed for {job_url}: {str(e)}")
            return None
    
    def _fallback_extraction(self, driver) -> Dict[str, Any]:
        """Fallback extraction method when main extraction fails"""
        fallback_data = {}
        try:
            # Try to extract basic fields using multiple strategies
            fallback_script = """
            const data = {};
            
            // Title extraction with multiple selectors
            const titleSelectors = [
                'h1[id*="job-title"]',
                'h1[data-test="job-title"]',
                '.JobDetails_jobDetailsHeader__Hd9M3 h1',
                'h1',
                '[class*="job-title"]',
                '[data-test*="title"]'
            ];
            
            for (const selector of titleSelectors) {
                const el = document.querySelector(selector);
                if (el && el.textContent.trim()) {
                    data.title = el.textContent.trim();
                    break;
                }
            }
            
            // Company extraction
            const companySelectors = [
                '.EmployerProfile_employerNameHeading__bXBYr h4',
                '[data-test="employer-name"]',
                '.EmployerProfile_profileContainer__* h4',
                'h4',
                '[class*="company"]',
                '[class*="employer"]'
            ];
            
            for (const selector of companySelectors) {
                const el = document.querySelector(selector);
                if (el && el.textContent.trim() && el.textContent.trim().length < 100) {
                    data.company = el.textContent.trim();
                    break;
                }
            }
            
            // Location extraction
            const locationSelectors = [
                '[data-test="location"]',
                '.JobDetails_locationAndPay__XGFmY div',
                '[class*="location"]',
                '[data-test*="location"]'
            ];
            
            for (const selector of locationSelectors) {
                const el = document.querySelector(selector);
                if (el && el.textContent.trim() && (el.textContent.includes(',') || el.textContent.length < 50)) {
                    data.location = el.textContent.trim();
                    break;
                }
            }
            
            // Salary extraction
            const salarySelectors = [
                '[data-test="detailSalary"]',
                '.JobCard_salaryEstimate__QpbTW',
                '[class*="salary"]',
                '[data-test*="salary"]'
            ];
            
            for (const selector of salarySelectors) {
                const el = document.querySelector(selector);
                if (el && el.textContent.trim()) {
                    data.salary = el.textContent.trim();
                    break;
                }
            }
            
            // Job description extraction
            const descSelectors = [
                '.JobDetails_jobDescription__uW_fK',
                '[data-test="jobDescriptionContent"]',
                '.job-description',
                '[class*="description"]'
            ];
            
            for (const selector of descSelectors) {
                const el = document.querySelector(selector);
                if (el && el.innerHTML.trim()) {
                    data.job_desc_html = el.outerHTML;
                    break;
                }
            }
            
            return data;
            """
            
            fallback_data = driver.execute_script(fallback_script)
            
        except Exception as e:
            self.logger.debug(f"Fallback extraction failed: {e}")
        
        return fallback_data

    def _generate_clean_markdown(self, sections: dict) -> str:
        """Generate clean, well-formatted markdown with deduplication"""
        md_parts = []

        # Define section order for better presentation
        section_order = [
            'job description',
            'required education and experience',
            'education',
            'required knowledge, skills and abilities',
            'most_relevant_skills',
            'other_relevant_skills',
            'responsibilities',
            'requirements',
            'qualifications',
            'benefits',
            'work_location',
            'schedule',
            'pay'
        ]

        # Process sections in order
        processed_sections = set()

        for section_key in section_order:
            if section_key in sections and section_key not in processed_sections:
                content = sections[section_key]
                if content:
                    # Format section header
                    header = section_key.replace('_', ' ').title()
                    md_parts.append(f"## {header}")

                    # Format content
                    if isinstance(content, list):
                        if len(content) == 1:
                            md_parts.append(content[0])
                        else:
                            for item in content:
                                if isinstance(item, str) and len(item.strip()) > 0:
                                    md_parts.append(f"• {item.strip()}")
                    else:
                        content_str = str(content).strip()
                        if content_str:
                            md_parts.append(content_str)

                    md_parts.append("")  # Add spacing
                    processed_sections.add(section_key)

        # Add any remaining sections
        for section_key, content in sections.items():
            if section_key not in processed_sections and content:
                header = section_key.replace('_', ' ').title()
                md_parts.append(f"## {header}")

                if isinstance(content, list):
                    if len(content) == 1:
                        md_parts.append(content[0])
                    else:
                        for item in content:
                            if isinstance(item, str) and len(item.strip()) > 0:
                                md_parts.append(f"• {item.strip()}")
                else:
                    content_str = str(content).strip()
                    if content_str:
                        md_parts.append(content_str)

                md_parts.append("")

        return "\n".join(md_parts).strip()

    def _quick_markdown_generation(self, sections: dict) -> str:
        """Fast markdown generation without heavy processing (fallback)"""
        md_parts = []
        for section, content in sections.items():
            if content:
                md_parts.append(f"### {section.title()}")
                if isinstance(content, list):
                    md_parts.extend(f"- {item}" for item in content[:5])  # Limit items
                else:
                    md_parts.append(str(content)[:500])  # Limit length
        return "\n".join(md_parts)

    def _extract_job_id(self, url: str) -> Optional[str]:
        """Extract job ID from URL"""
        patterns = [r'jl=(\d+)', r'jobListingId=(\d+)', r'/(\d+)\.htm']
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None

    def scrape_jobs_parallel(self, job_title: str, location: str, num_jobs: int = 5) -> dict:
        """
        Parallel scraping using multiple processes. Collects job URLs in the main process, then scrapes job details in parallel.
        Returns the same structure as scrape_jobs.
        """
        start_time = time.time()
        metadata = {
            'start_time': datetime.utcnow().isoformat(),
            'job_title': job_title,
            'location': location,
            'requested_jobs': num_jobs,
            'scraped_jobs': 0,
            'failed_jobs': 0,
            'errors': [],
            'failed_urls': []
        }
        jobs = []
        
        try:
            # Step 1: Collect job URLs in main process with optimized driver
            driver = self.driver_manager.create_cloudflare_resistant_driver(1)
            try:
                self._perform_search(driver, job_title, location)
                # Collect exactly the number of URLs requested, not 2x
                job_urls = self._collect_job_urls_fast(driver, num_jobs)
                self.logger.info(f"Found {len(job_urls)} job URLs for parallel scraping (requested: {num_jobs})")
            finally:
                driver.quit()
                
            if not job_urls:
                metadata['errors'].append('No job URLs found')
                metadata['execution_time'] = time.time() - start_time
                metadata['end_time'] = datetime.utcnow().isoformat()
                return {'scraped_jobs': [], 'metadata': metadata}
            
            # Step 2: Use ThreadPoolExecutor instead of ProcessPoolExecutor for better reliability
            # Split URLs into smaller chunks to avoid memory issues
            max_workers = min(3, len(job_urls))  # Max 3 threads to avoid detection
            chunk_size = max(1, ceil(len(job_urls) / max_workers))
            url_chunks = [job_urls[i:i + chunk_size] for i in range(0, len(job_urls), chunk_size)]
            
            self.logger.info(f"Processing {len(job_urls)} URLs in {len(url_chunks)} chunks with {max_workers} workers")
            
            # Step 3: Scrape in parallel using ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=max_workers) as pool:
                futures = []
                for i, chunk in enumerate(url_chunks):
                    # Stagger the start of each thread to avoid simultaneous requests
                    self.logger.info(f"Submitting chunk {i+1}/{len(url_chunks)} with {len(chunk)} URLs")
                    future = pool.submit(self._scrape_url_chunk_threaded, chunk, i * 3)
                    futures.append(future)
                
                # Collect results with timeout
                completed_chunks = 0
                for future in as_completed(futures, timeout=300):  # 5 minute timeout
                    try:
                        completed_chunks += 1
                        self.logger.info(f"Processing completed chunk {completed_chunks}/{len(url_chunks)}")
                        chunk_jobs = future.result(timeout=60)  # 1 minute per chunk
                        
                        valid_jobs = 0
                        for job in chunk_jobs:
                            if job and job.get('title'):
                                jobs.append(job)
                                valid_jobs += 1
                                if len(jobs) >= num_jobs:  # Stop when we have enough
                                    self.logger.info(f"Reached target of {num_jobs} jobs, stopping processing")
                                    break
                            else:
                                metadata['failed_jobs'] += 1
                        
                        self.logger.info(f"Chunk {completed_chunks} completed: {valid_jobs} valid jobs, {len(chunk_jobs) - valid_jobs} failed")
                        
                        if len(jobs) >= num_jobs:  # Stop processing if we have enough
                            break
                    except Exception as e:
                        self.logger.error(f"Chunk {completed_chunks} processing failed: {str(e)}")
                        metadata['errors'].append(str(e))
            
            # Limit to requested number of jobs
            jobs = jobs[:num_jobs]
            
            metadata.update({
                'scraped_jobs': len(jobs),
                'execution_time': time.time() - start_time,
                'end_time': datetime.utcnow().isoformat(),
                'success_rate': f"{len(jobs)}/{len(job_urls)} ({len(jobs)/len(job_urls)*100:.1f}%)" if job_urls else "0%"
            })
            
            self.logger.info(f"Parallel scraping completed: {len(jobs)} jobs in {metadata['execution_time']:.2f}s")
            return {'scraped_jobs': jobs, 'metadata': metadata}
            
        except Exception as e:
            self.logger.error(f"Parallel scraping failed: {str(e)}")
            metadata['errors'].append(str(e))
            metadata['execution_time'] = time.time() - start_time
            metadata['end_time'] = datetime.utcnow().isoformat()
            return {'scraped_jobs': jobs, 'metadata': metadata}

    def _scrape_url_chunk_threaded(self, urls: list, delay_seconds: int = 0) -> list:
        """
        Thread-safe helper for parallel scraping: launches a new driver, scrapes all jobs in the chunk, returns list of dicts.
        """
        # Wait before starting to stagger requests
        if delay_seconds > 0:
            time.sleep(delay_seconds)
        
        # Enhanced anti-detection config for worker
        chrome_driver_path = '/Users/<USER>/Desktop/job_portal/chromedriver'
        options = Options()
        options.add_argument('--headless=new')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_experimental_option("prefs", {
            "profile.default_content_setting_values": {
                "images": 2, 
                "media_stream": 2, 
                "stylesheets": 1,
                "javascript": 1  # Keep JS enabled for extraction
            },
            "profile.managed_default_content_settings": {"images": 2}
        })
        
        # Random user agent
        user_agents = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        ]
        options.add_argument(f'--user-agent={random.choice(user_agents)}')
        
        service = Service(chrome_driver_path)
        driver = webdriver.Chrome(service=service, options=options)
        
        # Execute stealth script
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        results = []
        try:
            for i, url in enumerate(urls):
                try:
                    # Add random delay between requests
                    if i > 0:
                        time.sleep(random.uniform(2, 4))  # Reduced delay for faster processing
                    
                    driver.get(url)
                    
                    # Wait for page load with multiple selectors
                    WebDriverWait(driver, 8).until(  # Reduced timeout
                        EC.any_of(
                            EC.presence_of_element_located((By.TAG_NAME, "h1")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, "[data-test='job-title']")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".JobDetails_jobDetailsHeader__Hd9M3")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, "[class*='job-title']")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, "body"))
                        )
                    )
                    
                    # Check for anti-bot page
                    page_text = driver.page_source.lower()
                    if "help us protect glassdoor" in page_text or "blocked" in page_text:
                        results.append({
                            'title': None, 
                            'error': 'Blocked by Glassdoor protection', 
                            'jd_url': url,
                            'company': None,
                            'job_description': None
                        })
                        continue
                    
                    # Expand show more button if present
                    try:
                        show_more_selectors = [
                            "button[data-test='show-more-cta']",
                            "button[data-test='show-more']", 
                            "button.css-1gpqj0y",
                            "[class*='show-more']",
                            "button:contains('Show more')",
                            "button:contains('Read more')"
                        ]
                        
                        for selector in show_more_selectors:
                            try:
                                if selector.startswith("button:contains"):
                                    text_content = selector.split('(')[1].split(')')[0].strip("'")
                                    xpath = f"//button[contains(text(), '{text_content}')]"
                                    show_more = driver.find_element(By.XPATH, xpath)
                                else:
                                    show_more = driver.find_element(By.CSS_SELECTOR, selector)
                                    
                                if show_more.is_displayed() and show_more.is_enabled():
                                    driver.execute_script("arguments[0].click();", show_more)
                                    time.sleep(1)
                                    break
                            except Exception:
                                continue
                    except Exception:
                        pass
                    
                    # Use the same sophisticated extraction as the simple endpoint
                    data = driver.execute_script('''
                        const data = {};
                        
                        // Title extraction with multiple selectors
                        const titleSelectors = [
                            "h1[id*='job-title']", 
                            "h1[data-test='job-title']", 
                            "h1.css-1qaijid",
                            ".JobDetails_jobTitle__Nw_N2",
                            "h1"
                        ];
                        for (let selector of titleSelectors) {
                            const title = document.querySelector(selector);
                            if (title && title.textContent.trim()) {
                                data.title = title.textContent.trim();
                                break;
                            }
                        }
                        
                        // Company extraction
                        const companySelectors = [
                            ".EmployerProfile_employerNameHeading__bXBYr",
                            "[data-test='employer-name']",
                            ".EmployerProfile_profileContainer__d6vLt h4",
                            "h4"
                        ];
                        for (let selector of companySelectors) {
                            const company = document.querySelector(selector);
                            if (company && company.textContent.trim()) {
                                data.company = company.textContent.trim();
                                break;
                            }
                        }
                        
                        // Location extraction
                        const locationSelectors = [
                            "[data-test='location']",
                            ".JobDetails_location__mSg5h",
                            "[data-test='job-location']"
                        ];
                        for (let selector of locationSelectors) {
                            const location = document.querySelector(selector);
                            if (location && location.textContent.trim()) {
                                data.location = location.textContent.trim();
                                break;
                            }
                        }
                        
                        // Salary extraction
                        const salarySelectors = [
                            "[data-test='detailSalary']",
                            ".JobDetails_salary__6VyJK",
                            "[data-test='salary']"
                        ];
                        for (let selector of salarySelectors) {
                            const salary = document.querySelector(selector);
                            if (salary && salary.textContent.trim()) {
                                data.salary = salary.textContent.trim();
                                break;
                            }
                        }
                        
                        // Job description extraction
                        const descSelectors = [
                            ".JobDetails_jobDescription__uW_fK",
                            ".JobDetails_jobDescription__6VeBn",
                            "[data-test='jobDescriptionContent']",
                            ".job-description"
                        ];
                        for (let selector of descSelectors) {
                            const desc = document.querySelector(selector);
                            if (desc && desc.innerHTML.trim()) {
                                data.job_desc_html = desc.outerHTML;
                                break;
                            }
                        }
                        
                        // Check for easy apply button
                        const easyApplySelectors = [
                            "button[data-test='easyApply']",
                            "button[data-test='apply-button']",
                            "button[data-test='applyButton']",
                            "button.css-1n6j6mr",
                            ".JobDetails_applyButtonContainer__L36Bs button"
                        ];
                        data.easy_apply = false;
                        for (let selector of easyApplySelectors) {
                            const btn = document.querySelector(selector);
                            if (btn && btn.offsetParent !== null) { // Check if visible
                                data.easy_apply = true;
                                break;
                            }
                        }
                        
                        return data;
                    ''')
                    
                    # Validate that we got basic data
                    if not data or not data.get('title'):
                        results.append({
                            'title': None, 
                            'error': 'Failed to extract job title', 
                            'jd_url': url,
                            'company': None,
                            'job_description': None
                        })
                        continue
                    
                    # Process job description using the same sophisticated logic as simple endpoint
                    job_desc = None
                    extra_sections = {}
                    if data.get('job_desc_html'):
                        try:
                            soup = BeautifulSoup(data['job_desc_html'], 'html.parser')
                            
                            # Use the same section extraction logic as the simple endpoint
                            sections = {}
                            desc_div = soup.find("div", class_="JobDetails_jobDescription__uW_fK")
                            
                            if not desc_div:
                                # Try fallback selectors
                                for selector in ['.JobDetails_jobDescription__6VeBn', '[data-test="jobDescriptionContent"]']:
                                    desc_div = soup.select_one(selector.replace('div.', '').replace('div', ''))
                                    if desc_div:
                                        break
                            
                            if desc_div:
                                # Process HTML structure using the same logic as simple endpoint
                                current_section = None
                                current_content = []
                                
                                for element in desc_div.descendants:
                                    if hasattr(element, 'name'):
                                        if element.name in ['h1', 'h2', 'h3', 'h4', 'b', 'strong']:
                                            # Save previous section
                                            if current_section and current_content:
                                                # Clean section name and avoid conflicts with main fields
                                                clean_section = current_section.lower().strip()
                                                if clean_section not in ['job title', 'job location', 'shift', 'qualification', 'salary', 'pay']:
                                                    sections[clean_section] = current_content
                                            
                                            # Start new section
                                            section_text = element.get_text(strip=True).lower().rstrip(':')
                                            current_section = section_text
                                            current_content = []
                                            
                                        elif element.name in ['p', 'li', 'div'] and current_section:
                                            text = element.get_text(strip=True)
                                            if text and len(text) > 3:  # Filter out very short text
                                                # Clean up common artifacts
                                                cleaned_text = text
                                                # Remove website URLs and artifacts
                                                cleaned_text = re.sub(r'\b\w+\.com\+\d+\b', '', cleaned_text)
                                                cleaned_text = re.sub(r'\b\w+\.org\+\d+\b', '', cleaned_text)
                                                cleaned_text = re.sub(r'\b\w+\.in\+\d+\b', '', cleaned_text)
                                                cleaned_text = re.sub(r'\b\w+\.net\+\d+\b', '', cleaned_text)
                                                # Remove standalone URLs
                                                cleaned_text = re.sub(r'https?://[^\s]+', '', cleaned_text)
                                                # Remove extra whitespace and punctuation artifacts
                                                cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
                                                cleaned_text = re.sub(r'[^\w\s\-.,!?;:()]', '', cleaned_text)
                                                cleaned_text = cleaned_text.strip()
                                                
                                                if cleaned_text and len(cleaned_text) > 5:
                                                    current_content.append(cleaned_text)
                                
                                # Save last section
                                if current_section and current_content:
                                    clean_section = current_section.lower().strip()
                                    if clean_section not in ['job title', 'job location', 'shift', 'qualification', 'salary', 'pay']:
                                        sections[clean_section] = current_content
                                
                                # Extract specific fields from sections (avoid conflicts)
                                for section_name, content in sections.items():
                                    if 'benefit' in section_name and not data.get('benefits'):
                                        data['benefits'] = content
                                    elif 'schedule' in section_name and not data.get('schedule'):
                                        data['schedule'] = content
                                    elif 'education' in section_name or 'qualification' in section_name:
                                        if not data.get('education'):
                                            data['education'] = content
                                    elif 'skill' in section_name or 'technology' in section_name:
                                        if not data.get('other_relevant_skills'):
                                            data['other_relevant_skills'] = content
                                    elif 'responsibility' in section_name:
                                        if not data.get('responsibilities'):
                                            data['responsibilities'] = content
                                    elif 'requirement' in section_name or 'qualification' in section_name:
                                        if not data.get('requirements'):
                                            data['requirements'] = content
                                
                                # Store sections for extra_sections (but clean them up)
                                extra_sections = {}
                                for section_name, content in sections.items():
                                    # Skip sections that are already mapped to main fields
                                    if section_name not in ['benefits', 'schedule', 'education', 'skills', 'responsibilities', 'requirements']:
                                        # Clean up the content to avoid duplicates
                                        if isinstance(content, list):
                                            # Remove duplicates and very short items
                                            cleaned_content = []
                                            seen_items = set()
                                            for item in content:
                                                clean_item = item.strip()
                                                # Additional cleaning for artifacts
                                                clean_item = re.sub(r'\b\w+\.com\+\d+\b', '', clean_item)
                                                clean_item = re.sub(r'\b\w+\.org\+\d+\b', '', clean_item)
                                                clean_item = re.sub(r'\b\w+\.in\+\d+\b', '', clean_item)
                                                clean_item = re.sub(r'\b\w+\.net\+\d+\b', '', clean_item)
                                                clean_item = re.sub(r'https?://[^\s]+', '', clean_item)
                                                clean_item = re.sub(r'\s+', ' ', clean_item)
                                                clean_item = clean_item.strip()
                                                
                                                if clean_item and len(clean_item) > 5 and clean_item not in seen_items:
                                                    cleaned_content.append(clean_item)
                                                    seen_items.add(clean_item)
                                            if cleaned_content:
                                                extra_sections[section_name] = cleaned_content
                                        else:
                                            # Clean single content items too
                                            if isinstance(content, str):
                                                clean_content = re.sub(r'\b\w+\.com\+\d+\b', '', content)
                                                clean_content = re.sub(r'\b\w+\.org\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'\b\w+\.in\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'\b\w+\.net\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'https?://[^\s]+', '', clean_content)
                                                clean_content = re.sub(r'\s+', ' ', clean_content)
                                                clean_content = clean_content.strip()
                                                if clean_content and len(clean_content) > 5:
                                                    extra_sections[section_name] = clean_content
                                            else:
                                                extra_sections[section_name] = content
                                
                                # Generate clean markdown using the same logic as simple endpoint
                                if sections:
                                    md_parts = []
                                    for section, content in sections.items():
                                        if content:
                                            md_parts.append(f"### {section.title()}")
                                            if isinstance(content, list):
                                                # Clean each item before adding to markdown
                                                clean_items = []
                                                for item in content[:5]:  # Limit items
                                                    clean_item = re.sub(r'\b\w+\.com\+\d+\b', '', str(item))
                                                    clean_item = re.sub(r'\b\w+\.org\+\d+\b', '', clean_item)
                                                    clean_item = re.sub(r'\b\w+\.in\+\d+\b', '', clean_item)
                                                    clean_item = re.sub(r'\b\w+\.net\+\d+\b', '', clean_item)
                                                    clean_item = re.sub(r'https?://[^\s]+', '', clean_item)
                                                    clean_item = re.sub(r'\s+', ' ', clean_item)
                                                    clean_item = clean_item.strip()
                                                    if clean_item and len(clean_item) > 5:
                                                        clean_items.append(clean_item)
                                                md_parts.extend(f"- {item}" for item in clean_items)
                                            else:
                                                # Clean single content
                                                clean_content = re.sub(r'\b\w+\.com\+\d+\b', '', str(content))
                                                clean_content = re.sub(r'\b\w+\.org\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'\b\w+\.in\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'\b\w+\.net\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'https?://[^\s]+', '', clean_content)
                                                clean_content = re.sub(r'\s+', ' ', clean_content)
                                                clean_content = clean_content.strip()
                                                if clean_content and len(clean_content) > 10:
                                                    md_parts.append(clean_content[:500])  # Limit length
                                    job_desc = "\n".join(md_parts)
                                else:
                                    # Fallback: convert HTML to simple text
                                    job_desc = soup.get_text(separator='\n', strip=True)[:1000]
                            
                        except Exception as e:
                            # If parsing fails, convert HTML to simple text
                            try:
                                soup = BeautifulSoup(data['job_desc_html'], 'html.parser')
                                job_desc = soup.get_text(separator='\n', strip=True)[:1000]
                            except:
                                job_desc = "Job description not available"
                    
                    # Extract additional fields using the same logic as simple endpoint
                    if data.get('job_desc_html'):
                        try:
                            soup = BeautifulSoup(data['job_desc_html'], 'html.parser')
                            desc_text = soup.get_text(separator=' ').lower()
                            
                            # Extract job type (only if not already set)
                            if not data.get('job_type'):
                                if 'full-time' in desc_text or 'full time' in desc_text:
                                    data['job_type'] = 'Full-time'
                                elif 'part-time' in desc_text or 'part time' in desc_text:
                                    data['job_type'] = 'Part-time'
                                elif 'contract' in desc_text:
                                    data['job_type'] = 'Contract'
                                elif 'internship' in desc_text:
                                    data['job_type'] = 'Internship'
                            
                            # Extract work location (only if not already set)
                            if not data.get('work_location'):
                                if 'remote' in desc_text or 'work from home' in desc_text:
                                    data['work_location'] = 'Remote'
                                elif 'hybrid' in desc_text:
                                    data['work_location'] = 'Hybrid'
                                elif 'on-site' in desc_text or 'onsite' in desc_text or 'in person' in desc_text:
                                    data['work_location'] = 'On-site'
                            
                            # Extract skills using the same logic as simple endpoint (only if not already set)
                            if not data.get('most_relevant_skills'):
                                skills = []
                                skill_keywords = ['python', 'java', 'javascript', 'sql', 'machine learning', 'ai', 'data science', 'react', 'node.js', 'aws', 'azure', 'docker', 'kubernetes', 'html', 'css', 'git', 'agile', 'scrum']
                                for skill in skill_keywords:
                                    if skill in desc_text:
                                        skills.append(skill)
                                data['most_relevant_skills'] = skills
                            
                        except Exception as e:
                            pass
                    
                    # Extract job ID from URL
                    job_id_match = re.search(r'jl=(\d+)', url)
                    if job_id_match:
                        data['job_id'] = job_id_match.group(1)
                    
                    # Clean up data and ensure proper structure
                    cleaned_data = {
                        'title': data.get('title', ''),
                        'company_name': data.get('company'),
                        'location': data.get('location'),
                        'salary': data.get('salary'),
                        'job_type': data.get('job_type'),
                        'work_location': data.get('work_location'),
                        'benefits': data.get('benefits'),
                        'schedule': data.get('schedule'),
                        'education': data.get('education'),
                        'most_relevant_skills': data.get('most_relevant_skills', []),
                        'other_relevant_skills': data.get('other_relevant_skills', []),
                        'easy_apply': data.get('easy_apply', False),
                        'job_description': job_desc,
                        'extra_sections': extra_sections,
                        'job_id': data.get('job_id'),
                        'jd_url': url,
                    }
                    
                    # Clean up list fields and ensure they're strings
                    for field in ['benefits', 'schedule', 'education']:
                        if isinstance(cleaned_data[field], list):
                            cleaned_data[field] = ' '.join([str(item) for item in cleaned_data[field] if item]).strip()
                        elif cleaned_data[field] is None:
                            cleaned_data[field] = None
                    
                    # Ensure skills are lists and clean them
                    for field in ['most_relevant_skills', 'other_relevant_skills']:
                        if not isinstance(cleaned_data[field], list):
                            if cleaned_data[field]:
                                cleaned_data[field] = [str(cleaned_data[field])]
                            else:
                                cleaned_data[field] = []
                        else:
                            # Clean skills list
                            clean_skills = []
                            for skill in cleaned_data[field]:
                                if isinstance(skill, str):
                                    # Clean skill text
                                    clean_skill = re.sub(r'\b\w+\.com\+\d+\b', '', skill)
                                    clean_skill = re.sub(r'\b\w+\.org\+\d+\b', '', clean_skill)
                                    clean_skill = re.sub(r'\b\w+\.in\+\d+\b', '', clean_skill)
                                    clean_skill = re.sub(r'\b\w+\.net\+\d+\b', '', clean_skill)
                                    clean_skill = re.sub(r'https?://[^\s]+', '', clean_skill)
                                    clean_skill = re.sub(r'\s+', ' ', clean_skill)
                                    clean_skill = clean_skill.strip()
                                    if clean_skill and len(clean_skill) > 2:
                                        clean_skills.append(clean_skill)
                            cleaned_data[field] = clean_skills
                    
                    # Final cleanup: remove any duplicate or conflicting data from extra_sections
                    if cleaned_data.get('extra_sections'):
                        final_extra_sections = {}
                        main_fields = ['title', 'company_name', 'location', 'salary', 'job_type', 'work_location', 'benefits', 'schedule', 'education']
                        
                        for section_name, content in cleaned_data['extra_sections'].items():
                            # Skip sections that might conflict with main fields
                            if section_name.lower() not in [field.lower() for field in main_fields]:
                                # Also skip very generic section names
                                if section_name.lower() not in ['job', 'location', 'shift', 'qualification', 'pay']:
                                    final_extra_sections[section_name] = content
                        
                        cleaned_data['extra_sections'] = final_extra_sections
                    
                    # Remove any None values that might cause issues
                    cleaned_data = {k: v for k, v in cleaned_data.items() if v is not None or k in ['title', 'jd_url']}
                    
                    results.append(cleaned_data)
                    
                except Exception as e:
                    results.append({
                        'title': None, 
                        'error': str(e), 
                        'jd_url': url,
                        'company_name': None,
                        'job_description': None
                    })
        finally:
            driver.quit()
        return results

    @staticmethod
    def _scrape_url_chunk(urls: list) -> list:
        """
        Legacy helper for parallel scraping: launches a new driver, scrapes all jobs in the chunk, returns list of dicts.
        """
        return GlassdoorScraper._scrape_url_chunk_with_delay(urls, 0)

# --- FastAPI endpoint and CLI entry point ---

app = FastAPI()

# Define allowed origins including ngrok URLs
allowed_origins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://localhost:3001",
    "https://localhost:3001",
    "https://0305-103-247-7-151.ngrok-free.app",
    "https://7ea9-103-247-7-151.ngrok-free.app",
    "https://65d0-202-148-58-240.ngrok-free.app",
    "https://530dc06a5900.ngrok-free.app",  # Added for CORS
    "*"  # Allow all origins for development
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,  # Set to False when using "*" or multiple origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

scraper_instance = GlassdoorScraper()

# Request model for POST endpoint
class GlassdoorRequest(BaseModel):
    job_title: str
    location: str
    num_jobs: int = 5

@app.get("/scrape_jobs")
def scrape_jobs_api(job_title: str = Query(...), location: str = Query(...), num_jobs: int = Query(5, ge=1, le=50)):
    """API endpoint to scrape Glassdoor jobs (GET)"""
    result = scraper_instance.scrape_jobs(job_title, location, num_jobs)
    return JSONResponse(content=result)

@app.post("/scrape_jobs")
def scrape_jobs_post_api(request: GlassdoorRequest):
    """API endpoint to scrape Glassdoor jobs (POST)"""
    result = scraper_instance.scrape_jobs(request.job_title, request.location, request.num_jobs)
    return JSONResponse(content=result)

@app.options("/scrape_jobs")
async def options_scrape_jobs():
    return {"message": "OK"}

@app.options("/scrape_jobs")
async def options_scrape_jobs_post():
    return {"message": "OK"}

@app.get("/scrape_jobs_parallel")
def scrape_jobs_parallel_api(job_title: str = Query(...), location: str = Query(...), num_jobs: int = Query(5, ge=1, le=50)):
    """
    API endpoint to scrape Glassdoor jobs in parallel (GET).
    """
    try:
        result = scraper_instance.scrape_jobs_parallel(job_title, location, num_jobs)
        return JSONResponse(content=result)
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": f"Parallel scraping failed: {str(e)}",
                "scraped_jobs": [],
                "metadata": {
                    "error": str(e),
                    "requested_jobs": num_jobs,
                    "scraped_jobs": 0
                }
            }
        )

@app.post("/scrape_jobs_parallel")
def scrape_jobs_parallel_post_api(request: GlassdoorRequest):
    """
    API endpoint to scrape Glassdoor jobs in parallel (POST).
    """
    try:
        result = scraper_instance.scrape_jobs_parallel(request.job_title, request.location, request.num_jobs)
        return JSONResponse(content=result)
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": f"Parallel scraping failed: {str(e)}",
                "scraped_jobs": [],
                "metadata": {
                    "error": str(e),
                    "requested_jobs": request.num_jobs,
                    "scraped_jobs": 0
                }
            }
        )

@app.options("/scrape_jobs_parallel")
async def options_scrape_jobs_parallel():
    return {"message": "OK"}

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "serve":
        uvicorn.run("new_glassdoor:app", host="0.0.0.0", port=8000, reload=True)
    else:
        job_title = sys.argv[1] if len(sys.argv) > 1 else "Software Engineer"
        location = sys.argv[2] if len(sys.argv) > 2 else "San Francisco, CA"
        num_jobs = int(sys.argv[3]) if len(sys.argv) > 3 else 5
        result = scraper_instance.scrape_jobs(job_title, location, num_jobs)
        print(json.dumps(result, indent=2))