import sys
import time
import json
import logging
import random
import re
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException, ElementClickInterceptedException
from fastapi import FastAPI, Query, HTTPException, Body
from fastapi.responses import JSONResponse
import uvicorn
import undetected_chromedriver as uc
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = FastAPI(title="Foundit Job Scraper", version="2.0")

# Define allowed origins including ngrok URLs
allowed_origins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://localhost:3001",
    "https://localhost:3001",
    "https://0305-103-247-7-151.ngrok-free.app",
    "https://7ea9-103-247-7-151.ngrok-free.app",
    "https://65d0-202-148-58-240.ngrok-free.app",
    "https://445925a819f6.ngrok-free.app/",
    "*"  # Allow all origins for development
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,  # Set to False when using "*" or multiple origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

@dataclass
class JobData:
    """Data class for job information with proper typing"""
    job_id: Optional[str] = None
    title: Optional[str] = None
    company_name: Optional[str] = None
    location: Optional[str] = None
    experience: Optional[str] = None
    salary: Optional[str] = None
    job_type: Optional[str] = None
    posted_date: Optional[str] = None
    total_views: Optional[str] = None
    job_description: Optional[str] = None
    company_description: Optional[str] = None
    skills: Optional[List[str]] = None
    industry: Optional[str] = None
    function: Optional[str] = None
    roles: Optional[str] = None
    job_url: Optional[str] = None
    scraped_at: Optional[str] = None

    def __post_init__(self):
        if self.scraped_at is None:
            self.scraped_at = datetime.now().isoformat()
        if self.skills is None:
            self.skills = []

class FounditScraper:
    """Foundit scraper with cookie handling and comprehensive job extraction"""
    
    def __init__(self, headless: bool = True, timeout: int = 60):
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self.wait = None
        
        # Updated selectors for new foundit.in structure
        self.selectors = {
            'cookie_banner': '#cookieBanner',
            'accept_cookies': '#acceptAll',
            'job_search_input': '#Desktop-skillsAutoComplete--input',  # Updated ID
            'location_input': '#Desktop-locationAutoComplete--input',  # Updated ID
            'search_button': 'button[type="submit"], .search_submit_btn',
            'job_cards': 'div[data-index]',  # New structure uses data-index
            'job_card_container': '.flex.flex-col.gap-4.rounded-2xl',  # Individual job card
            'job_title': 'h3[title], h3.text-base.font-bold',
            'company_name': 'span p, .text-sm.font-normal p',
            'experience': 'label:contains("yrs"), svg + label',
            'location_card': 'svg[viewBox="0 0 24 24"] + label',
            'posted_date': 'label.text-xxs:contains("Posted"), .text-fontColor-content-tertiary',
            'apply_button': '#applyBtn, button:contains("Apply")',
            'save_button': 'button:contains("Save")',
            'company_logo': 'img[alt]',
            'next_page': '.pagination-next a, .next-page a, button:contains("Next")'
        }
    
    def setup_driver(self) -> uc.Chrome:
        """Setup Chrome driver with optimized options"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                options = uc.ChromeOptions()
                
                # Essential options for scraping
                if self.headless:
                    options.add_argument('--headless=new')
                    options.add_argument('--window-size=1920,1080')
                
                # Anti-detection options
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-blink-features=AutomationControlled')
                
                # Performance options
                options.add_argument('--disable-images')
                options.add_argument('--disable-extensions')
                options.add_argument('--disable-plugins')
                options.add_argument('--disable-gpu')
                options.add_argument('--disable-web-security')
                options.add_argument('--disable-features=VizDisplayCompositor')
                options.add_argument('--disable-logging')
                options.add_argument('--disable-dev-tools')
                options.add_argument('--no-first-run')
                options.add_argument('--no-default-browser-check')
                options.add_argument('--disable-default-apps')
                options.add_argument('--disable-popup-blocking')
                options.add_argument('--ignore-certificate-errors')
                options.add_argument('--ignore-ssl-errors')
                options.add_argument('--ignore-certificate-errors-spki-list')
                
                # Additional options for remote access
                options.add_argument('--remote-debugging-port=9222')
                options.add_argument('--disable-background-timer-throttling')
                options.add_argument('--disable-backgrounding-occluded-windows')
                options.add_argument('--disable-renderer-backgrounding')
                options.add_argument('--disable-field-trial-config')
                options.add_argument('--disable-ipc-flooding-protection')
                
                # User agent rotation
                user_agents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ]
                options.add_argument(f'--user-agent={random.choice(user_agents)}')
                
                # Force headless mode for remote access
                if self.headless:
                    options.add_argument('--headless=new')
                    options.add_argument('--disable-gpu')
                    options.add_argument('--no-sandbox')
                    options.add_argument('--disable-dev-shm-usage')
                
                logger.info(f"Attempting to initialize Chrome driver (attempt {attempt + 1}/{max_retries})")
                
                self.driver = uc.Chrome(options=options)
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                self.wait = WebDriverWait(self.driver, self.timeout)
                
                logger.info("Chrome driver initialized successfully")
                return self.driver
                
            except Exception as e:
                logger.error(f"Failed to setup driver (attempt {attempt + 1}/{max_retries}): {e}")
                if self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None
                
                if attempt == max_retries - 1:
                    raise WebDriverException(f"Driver setup failed after {max_retries} attempts: {e}")
                
                # Wait before retry
                time.sleep(2)
        
        raise WebDriverException("Failed to initialize Chrome driver")
    
    def random_delay(self, min_seconds: float = 1, max_seconds: float = 3):
        """Add random delay to avoid detection"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def handle_cookies(self):
        """Handle cookie consent banner if present"""
        try:
            # Check if cookie banner exists
            cookie_banner = self.driver.find_element(By.CSS_SELECTOR, self.selectors['cookie_banner'])
            if cookie_banner.is_displayed():
                logger.info("Cookie banner found, accepting cookies...")
                accept_button = self.driver.find_element(By.CSS_SELECTOR, self.selectors['accept_cookies'])
                accept_button.click()
                self.random_delay(1, 2)
                logger.info("Cookies accepted successfully")
        except NoSuchElementException:
            logger.info("No cookie banner found")
        except Exception as e:
            logger.warning(f"Error handling cookies: {e}")
    
    def safe_find_element(self, by: By, value: str, context=None) -> Optional[str]:
        """Safely find element and return text content"""
        try:
            element_context = context or self.driver
            element = element_context.find_element(by, value)
            return element.text.strip() if element.text else None
        except NoSuchElementException:
            return None
        except Exception as e:
            logger.warning(f"Error finding element {value}: {e}")
            return None
    
    def fill_search_form(self, job_title: str, location: str) -> bool:
        """Fill the search form on foundit.in homepage"""
        try:
            logger.info(f"Filling search form: {job_title} in {location}")

            # Navigate to homepage
            self.driver.get("https://www.foundit.in/")
            self.random_delay(2, 4)

            # Handle cookies first
            self.handle_cookies()

            # Find job title input using updated ID
            job_input = None
            job_input_ids = [
                'Desktop-skillsAutoComplete--input',  # Current ID
                'heroSectionDesktop-skillsAutoComplete--input'  # Fallback to old ID
            ]

            for input_id in job_input_ids:
                try:
                    job_input = self.driver.find_element(By.ID, input_id)
                    if job_input.is_displayed():
                        logger.info(f"Found job title input with ID: {input_id}")

                        # Clear field completely using multiple methods
                        job_input.clear()
                        self.random_delay(0.3, 0.5)

                        # Clear any existing value with Ctrl+A and Delete
                        job_input.send_keys(Keys.CONTROL + "a")
                        job_input.send_keys(Keys.DELETE)
                        self.random_delay(0.3, 0.5)

                        # Type the job title character by character to avoid autocomplete
                        for char in job_title:
                            job_input.send_keys(char)
                            time.sleep(0.05)  # Small delay between characters

                        self.random_delay(0.5, 1)

                        # Check if autocomplete dropdown appeared and dismiss it
                        try:
                            # Press Escape to close any autocomplete dropdown
                            job_input.send_keys(Keys.ESCAPE)
                            self.random_delay(0.3, 0.5)
                        except:
                            pass

                        # Verify the value was set correctly and remove any trailing commas
                        current_value = job_input.get_attribute('value')
                        logger.info(f"Job title field value after filling: '{current_value}'")

                        # If there's a trailing comma, remove it
                        if current_value.endswith(', ') or current_value.endswith(','):
                            logger.info("Removing trailing comma from job title field")
                            job_input.clear()
                            self.random_delay(0.2, 0.3)
                            clean_title = job_title.strip().rstrip(',').strip()
                            job_input.send_keys(clean_title)
                            self.random_delay(0.5, 1)
                            job_input.send_keys(Keys.ESCAPE)  # Dismiss autocomplete
                            current_value = job_input.get_attribute('value')
                            logger.info(f"Job title field value after cleaning: '{current_value}'")

                        logger.info(f"Successfully filled job title: {job_title}")
                        break

                except NoSuchElementException:
                    continue

            if not job_input:
                logger.warning("Could not find job title input field with any known ID")
                # Fallback to other selectors
                job_input_selectors = [
                    'input[placeholder*="Skills"]',
                    'input[placeholder*="Job Title"]',
                    'input[placeholder*="skill"]'
                ]

                for selector in job_input_selectors:
                    try:
                        job_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if job_input.is_displayed():
                            job_input.clear()
                            job_input.send_keys(Keys.CONTROL + "a")
                            job_input.send_keys(Keys.DELETE)
                            self.random_delay(0.3, 0.5)
                            job_input.send_keys(job_title)
                            logger.info(f"Filled job title: {job_title} using fallback selector: {selector}")
                            break
                    except NoSuchElementException:
                        continue

            # Find location input using updated ID
            location_input = None
            location_input_ids = [
                'Desktop-locationAutoComplete--input',  # Current ID
                'heroSectionDesktop-locationAutoComplete--input'  # Fallback to old ID
            ]

            for input_id in location_input_ids:
                try:
                    location_input = self.driver.find_element(By.ID, input_id)
                    if location_input.is_displayed():
                        logger.info(f"Found location input with ID: {input_id}")

                        # Clear field completely using multiple methods
                        location_input.clear()
                        self.random_delay(0.3, 0.5)

                        # Clear any existing value with Ctrl+A and Delete
                        location_input.send_keys(Keys.CONTROL + "a")
                        location_input.send_keys(Keys.DELETE)
                        self.random_delay(0.3, 0.5)

                        # Type the location character by character to avoid autocomplete
                        for char in location:
                            location_input.send_keys(char)
                            time.sleep(0.05)  # Small delay between characters

                        self.random_delay(0.5, 1)

                        # Check if autocomplete dropdown appeared and dismiss it
                        try:
                            # Press Escape to close any autocomplete dropdown
                            location_input.send_keys(Keys.ESCAPE)
                            self.random_delay(0.3, 0.5)
                        except:
                            pass

                        # Verify the value was set correctly and remove any trailing commas
                        current_value = location_input.get_attribute('value')
                        logger.info(f"Location field value after filling: '{current_value}'")

                        # If there's a trailing comma, remove it
                        if current_value.endswith(', ') or current_value.endswith(','):
                            logger.info("Removing trailing comma from location field")
                            location_input.clear()
                            self.random_delay(0.2, 0.3)
                            clean_location = location.strip().rstrip(',').strip()
                            location_input.send_keys(clean_location)
                            self.random_delay(0.5, 1)
                            location_input.send_keys(Keys.ESCAPE)  # Dismiss autocomplete
                            current_value = location_input.get_attribute('value')
                            logger.info(f"Location field value after cleaning: '{current_value}'")

                        logger.info(f"Successfully filled location: {location}")
                        break

                except NoSuchElementException:
                    continue

            if not location_input:
                logger.warning("Could not find location input field with any known ID")
                # Fallback to other selectors
                location_input_selectors = [
                    'input[placeholder="Location"]',
                    'input[placeholder*="location"]'
                ]

                for selector in location_input_selectors:
                    try:
                        location_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if location_input.is_displayed() and location_input != job_input:
                            location_input.clear()
                            location_input.send_keys(Keys.CONTROL + "a")
                            location_input.send_keys(Keys.DELETE)
                            self.random_delay(0.3, 0.5)
                            location_input.send_keys(location)
                            logger.info(f"Filled location: {location} using fallback selector: {selector}")
                            break
                    except NoSuchElementException:
                        continue

            # Wait a moment to ensure form fields are properly filled
            logger.info("Waiting before clicking search button to ensure form is ready...")
            self.random_delay(1, 2)

            # Find and click search button
            search_button = None
            search_button_selectors = [
                'button[type="submit"]',
                '.search_submit_btn',
                'button.search_submit_btn',
                'form button[type="submit"]'
            ]

            for selector in search_button_selectors:
                try:
                    search_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if search_button.is_displayed() and search_button.is_enabled():
                        logger.info(f"Found search button with selector: {selector}")
                        break
                except NoSuchElementException:
                    continue

            if search_button:
                try:
                    # Scroll to button if needed
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", search_button)
                    self.random_delay(1, 2)

                    # Try clicking with JavaScript if regular click fails
                    try:
                        search_button.click()
                        logger.info("Search button clicked successfully")
                    except ElementClickInterceptedException:
                        logger.info("Regular click intercepted, trying JavaScript click")
                        self.driver.execute_script("arguments[0].click();", search_button)
                        logger.info("Search button clicked with JavaScript")

                except Exception as e:
                    logger.error(f"Error clicking search button: {e}")
                    # Fallback to Enter key
                    if job_input:
                        job_input.send_keys(Keys.RETURN)
                        logger.info("Used Enter key as fallback")
            else:
                logger.warning("Could not find search button, trying Enter key on job input")
                if job_input:
                    job_input.send_keys(Keys.RETURN)
                    logger.info("Pressed Enter key on job input field")

            # Wait for page navigation and search results to load
            logger.info("Waiting for page navigation and search results to load...")

            try:
                # Get initial URL
                original_url = self.driver.current_url
                logger.info(f"Original URL: {original_url}")

                # Wait a moment for navigation to start
                self.random_delay(1, 2)

                # Check current URL after navigation
                current_url = self.driver.current_url
                logger.info(f"Current URL after navigation: {current_url}")

                # Check if we're on a search results page (URL contains search parameters)
                if '/search/' in current_url and ('query=' in current_url or 'Data+Scientist' in current_url):
                    logger.info("Successfully navigated to search results page")
                elif current_url != original_url:
                    logger.info(f"URL changed to: {current_url}")
                else:
                    logger.warning("URL did not change, checking for search results on same page")

                # Wait for search results to appear
                logger.info("Waiting for search results to load...")
                self.random_delay(3, 5)

                # Try multiple selectors for search results with more specific targeting
                search_result_selectors = [
                    'div[data-index]',  # New job cards with data-index
                    '.flex.w-full.flex-col.gap-3',  # Job card wrapper
                    '.rounded-2xl.border-opacity-50.p-4',  # Individual job card
                    '.text-base.font-bold',  # Job title elements
                    'h3[title]',  # Job titles with title attribute
                    '.no-results',  # No results message
                    '[class*="job"]',  # Any element with "job" in class name
                    '.search-results'
                ]

                element_found = False
                found_selector = None
                found_count = 0

                for selector in search_result_selectors:
                    try:
                        elements = WebDriverWait(self.driver, 15).until(
                            EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                        )
                        if elements:
                            logger.info(f"Found {len(elements)} elements with selector: {selector}")
                            element_found = True
                            found_selector = selector
                            found_count = len(elements)
                            break
                    except TimeoutException:
                        logger.debug(f"Timeout waiting for selector: {selector}")
                        continue

                if element_found:
                    logger.info(f"Search results detected: {found_count} elements found with '{found_selector}'")
                else:
                    logger.warning("No search results found with any selector")

                    # Check page title for "0 jobs" or similar indicators
                    page_title = self.driver.title
                    logger.info(f"Page title: {page_title}")

                    if ': 0 ' in page_title or 'no jobs' in page_title.lower() or '0 job vacancies' in page_title.lower():
                        logger.warning("Page title indicates no jobs found (0 results)")
                        return True  # This is a successful search with no results

                    # Check page source for no results indicators
                    try:
                        page_source = self.driver.page_source.lower()
                        no_results_indicators = [
                            '0 job vacancies',
                            'no jobs found',
                            'no results found',
                            'no matching jobs',
                            'sorry, no jobs found'
                        ]

                        for indicator in no_results_indicators:
                            if indicator in page_source:
                                logger.warning(f"Found 'no results' indicator in page source: '{indicator}'")
                                return True  # This is a successful search with no results

                        # Check for specific no results elements
                        no_results_selectors = [
                            '.no-results',
                            '[class*="no-result"]',
                            '.empty-state',
                            '[class*="empty"]'
                        ]

                        for no_result_selector in no_results_selectors:
                            try:
                                no_result_elem = self.driver.find_element(By.CSS_SELECTOR, no_result_selector)
                                if no_result_elem.is_displayed():
                                    logger.warning(f"Found 'no results' element with selector: {no_result_selector}")
                                    logger.warning(f"No results text: {no_result_elem.text}")
                                    return True  # This is a successful search with no results
                            except NoSuchElementException:
                                continue

                    except Exception as e:
                        logger.debug(f"Error checking for no results indicators: {e}")

                    # Log current page state for debugging
                    logger.info(f"Current URL after search: {self.driver.current_url}")
                    logger.info(f"Could not determine if this is a 'no results' page or a loading issue")

                self.random_delay(2, 3)
                logger.info("Search form processing completed")
                return True

            except Exception as e:
                logger.error(f"Error during search results loading: {e}")
                logger.info(f"Current URL: {self.driver.current_url}")
                logger.info(f"Page title: {self.driver.title}")
                return False

        except Exception as e:
            logger.error(f"Error filling search form: {e}")
            return False
    
    def extract_job_data_from_search_results(self) -> tuple:
        """Extract job data directly from search results page (new approach)"""
        jobs = []
        try:
            # Wait for job cards to load with comprehensive selectors
            job_cards = []
            job_card_selectors = [
                'div[data-index]',  # Primary selector for new structure
                '.flex.w-full.flex-col.gap-3',  # Job card wrapper
                '.rounded-2xl.border-opacity-50.p-4',  # Individual job card container
                '.flex.flex-col.gap-4.rounded-2xl',  # Alternative job card
                self.selectors['job_cards'],  # Fallback to original selector
                '[class*="job-card"]',  # Any element with job-card in class
                '[class*="card"]'  # Any card element
            ]

            found_selector = None
            for selector in job_card_selectors:
                try:
                    potential_cards = WebDriverWait(self.driver, 15).until(
                        EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                    )
                    if potential_cards:
                        job_cards = potential_cards
                        found_selector = selector
                        logger.info(f"Found {len(job_cards)} job cards with selector: {found_selector}")
                        break
                except TimeoutException:
                    logger.debug(f"No elements found with selector: {selector}")
                    continue

            if not job_cards:
                logger.warning("No job cards found with any selector")
                # Try to get page source snippet for debugging
                try:
                    page_source = self.driver.page_source
                    if 'no jobs found' in page_source.lower() or 'no results' in page_source.lower():
                        logger.warning("Page contains 'no jobs found' or 'no results' message")
                    else:
                        logger.info(f"Page source length: {len(page_source)} characters")
                        # Log first few div elements to understand structure
                        divs = self.driver.find_elements(By.TAG_NAME, 'div')[:10]
                        logger.info(f"Found {len(divs)} div elements on page")
                        for i, div in enumerate(divs[:5]):
                            classes = div.get_attribute('class') or 'no-class'
                            logger.debug(f"Div {i}: classes='{classes}'")
                except Exception as e:
                    logger.debug(f"Error analyzing page source: {e}")
                return jobs

            for index, card in enumerate(job_cards):
                try:
                    job = JobData()

                    # Extract job title
                    title_selectors = ['h3[title]', 'h3.text-base.font-bold', 'h3']
                    for title_sel in title_selectors:
                        try:
                            title_elem = card.find_element(By.CSS_SELECTOR, title_sel)
                            job.title = title_elem.get_attribute('title') or title_elem.text.strip()
                            if job.title:
                                break
                        except NoSuchElementException:
                            continue

                    # Extract company name
                    company_selectors = ['span p', '.text-sm.font-normal p', 'p']
                    for comp_sel in company_selectors:
                        try:
                            company_elem = card.find_element(By.CSS_SELECTOR, comp_sel)
                            job.company_name = company_elem.text.strip()
                            if job.company_name:
                                break
                        except NoSuchElementException:
                            continue

                    # Extract experience and location from labels
                    labels = card.find_elements(By.TAG_NAME, 'label')
                    for label in labels:
                        text = label.text.strip()
                        if 'yrs' in text.lower():
                            job.experience = text
                        elif any(city in text.lower() for city in ['bengaluru', 'bangalore', 'mumbai', 'delhi', 'chennai', 'hyderabad', 'pune']):
                            job.location = text
                        elif 'posted' in text.lower():
                            job.posted_date = text.replace('Posted', '').strip()

                    # Extract company logo
                    try:
                        logo_elem = card.find_element(By.CSS_SELECTOR, 'img[alt]')
                        job.company_description = f"Logo: {logo_elem.get_attribute('src')}"
                    except NoSuchElementException:
                        pass

                    # Set job ID as index for now (since new structure doesn't have explicit IDs)
                    job.job_id = str(index + 1)

                    # Try to find the job detail URL from clickable elements
                    job_detail_url = None
                    try:
                        # Method 1: Look for direct links with /job/ in href
                        links = card.find_elements(By.CSS_SELECTOR, 'a[href*="/job/"]')
                        if links:
                            job_detail_url = links[0].get_attribute('href')
                            logger.debug(f"Found job URL via direct link: {job_detail_url}")

                        # Method 2: Look for any links and check if they contain job URLs
                        if not job_detail_url:
                            all_links = card.find_elements(By.TAG_NAME, 'a')
                            for link in all_links:
                                href = link.get_attribute('href')
                                if href and '/job/' in href:
                                    job_detail_url = href
                                    logger.debug(f"Found job URL via link scan: {job_detail_url}")
                                    break

                        # Method 3: Look for onclick handlers that might contain job URLs
                        if not job_detail_url:
                            clickable_elements = card.find_elements(By.CSS_SELECTOR, '[onclick], .cursor-pointer')
                            for elem in clickable_elements:
                                onclick = elem.get_attribute('onclick')
                                if onclick and 'job' in onclick.lower():
                                    # Extract URL from onclick if possible
                                    import re
                                    url_match = re.search(r'["\']([^"\']*job[^"\']*)["\']', onclick)
                                    if url_match:
                                        potential_url = url_match.group(1)
                                        if not potential_url.startswith('http'):
                                            potential_url = f"https://www.foundit.in{potential_url}"
                                        job_detail_url = potential_url
                                        logger.debug(f"Found job URL via onclick: {job_detail_url}")
                                        break

                        # Method 4: Mark for click-through navigation (URL construction is unreliable)
                        if not job_detail_url and job.title and job.company_name:
                            # Set a flag to indicate this job needs click-through navigation
                            job.job_url = "NEEDS_NAVIGATION"
                            logger.debug(f"Job '{job.title}' marked for click-through navigation - no direct URL found")

                    except Exception as e:
                        logger.debug(f"Error finding job detail URL: {e}")

                    # Set job URL (either detail page or search page)
                    job.job_url = job_detail_url if job_detail_url else self.driver.current_url

                    # Log the URL for debugging
                    if job_detail_url:
                        logger.debug(f"Job detail URL found: {job_detail_url}")
                    else:
                        logger.debug(f"No job detail URL found for: {job.title}")

                    if job.title and job.company_name:
                        jobs.append(job)
                        logger.debug(f"Extracted job: {job.title} at {job.company_name}")

                except Exception as e:
                    logger.warning(f"Error extracting data from job card {index}: {e}")
                    continue

            logger.info(f"Extracted {len(jobs)} jobs from search results")

        except TimeoutException:
            logger.error("Timeout waiting for job cards to load")
        except Exception as e:
            logger.error(f"Error extracting job data: {e}")

        return jobs, job_cards

    def try_navigate_to_job_detail(self, card_element) -> str:
        """Try to navigate to job detail page by clicking on the card"""
        try:
            # Store current URL to return to it later
            search_url = self.driver.current_url

            # Try to find and click on the job title or card
            clickable_elements = [
                'h3[title]',  # Job title
                'h3',  # Any h3 (job title)
                'a',  # Any link
                '.cursor-pointer'  # Any clickable element
            ]

            for selector in clickable_elements:
                try:
                    clickable = card_element.find_element(By.CSS_SELECTOR, selector)
                    if clickable.is_displayed():
                        # Scroll to element and click
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", clickable)
                        self.random_delay(0.5, 1)

                        # Try clicking
                        try:
                            clickable.click()
                        except ElementClickInterceptedException:
                            # Try JavaScript click if regular click fails
                            self.driver.execute_script("arguments[0].click();", clickable)

                        self.random_delay(1, 2)

                        # Check if URL changed to a job detail page
                        current_url = self.driver.current_url
                        if current_url != search_url and '/job/' in current_url:
                            logger.info(f"Successfully navigated to job detail page: {current_url}")
                            return current_url
                        else:
                            # Navigate back to search results if URL didn't change properly
                            self.driver.get(search_url)
                            self.random_delay(1, 2)
                            break

                except NoSuchElementException:
                    continue
                except Exception as e:
                    logger.debug(f"Error clicking element with selector {selector}: {e}")
                    # Navigate back to search results
                    self.driver.get(search_url)
                    self.random_delay(1, 2)
                    continue

            return None

        except Exception as e:
            logger.error(f"Error trying to navigate to job detail: {e}")
            return None

    def extract_job_id_from_card(self, card_element) -> Optional[str]:
        """Extract job ID from various sources in the job card"""
        try:
            # Method 1: Look for hidden job ID in data attributes
            job_id_attrs = ['data-job-id', 'data-jobid', 'data-id', 'data-job']
            for attr in job_id_attrs:
                job_id = card_element.get_attribute(attr)
                if job_id and job_id.isdigit():
                    logger.debug(f"Found job ID via {attr}: {job_id}")
                    return job_id

            # Method 2: Look for job ID in onclick handlers
            clickable_elements = card_element.find_elements(By.CSS_SELECTOR, '[onclick]')
            for elem in clickable_elements:
                onclick = elem.get_attribute('onclick')
                if onclick:
                    # Look for numeric IDs in onclick handlers
                    import re
                    id_matches = re.findall(r'\b(\d{8,})\b', onclick)  # 8+ digit numbers
                    if id_matches:
                        job_id = id_matches[0]
                        logger.debug(f"Found job ID via onclick: {job_id}")
                        return job_id

            # Method 3: Look for job ID in href attributes
            links = card_element.find_elements(By.TAG_NAME, 'a')
            for link in links:
                href = link.get_attribute('href')
                if href:
                    # Extract job ID from URL patterns
                    import re
                    # Pattern for foundit.in job URLs
                    id_match = re.search(r'/job/[^/]+-(\d{8,})(?:\?|$)', href)
                    if id_match:
                        job_id = id_match.group(1)
                        logger.debug(f"Found job ID via href: {job_id}")
                        return job_id

            # Method 4: Look for job ID in text content (like "Job ID: 12345678")
            text_content = card_element.text
            if text_content:
                import re
                id_match = re.search(r'(?:job\s*id|id)\s*:?\s*(\d{8,})', text_content, re.IGNORECASE)
                if id_match:
                    job_id = id_match.group(1)
                    logger.debug(f"Found job ID via text content: {job_id}")
                    return job_id

            # Method 5: Look for job ID in nested elements
            nested_elements = card_element.find_elements(By.CSS_SELECTOR, '[id], [class]')
            for elem in nested_elements:
                elem_id = elem.get_attribute('id')
                elem_class = elem.get_attribute('class')

                # Check ID attribute
                if elem_id:
                    import re
                    id_match = re.search(r'(\d{8,})', elem_id)
                    if id_match:
                        job_id = id_match.group(1)
                        logger.debug(f"Found job ID via element ID: {job_id}")
                        return job_id

                # Check class attribute
                if elem_class:
                    import re
                    id_match = re.search(r'(\d{8,})', elem_class)
                    if id_match:
                        job_id = id_match.group(1)
                        logger.debug(f"Found job ID via element class: {job_id}")
                        return job_id

            logger.debug("No job ID found in card element")
            return None

        except Exception as e:
            logger.debug(f"Error extracting job ID: {e}")
            return None

    def create_url_slug(self, text: str) -> str:
        """Create URL-friendly slug from text"""
        if not text:
            return ""

        import re
        # Convert to lowercase
        slug = text.lower()

        # Replace spaces and special characters with hyphens
        slug = re.sub(r'[^\w\s-]', '', slug)  # Remove special chars except spaces and hyphens
        slug = re.sub(r'[-\s]+', '-', slug)   # Replace spaces and multiple hyphens with single hyphen

        # Remove leading/trailing hyphens
        slug = slug.strip('-')

        # Limit length to reasonable size
        if len(slug) > 50:
            slug = slug[:50].rstrip('-')

        return slug

    def create_enhanced_location_slug(self, location: str) -> str:
        """Create enhanced location slug that matches foundit.in format"""
        if not location:
            return "india"

        # Common location mappings for Indian cities
        location_mappings = {
            'hyderabad': 'hyderabad-secunderabad-telangana',
            'bangalore': 'bengaluru-bangalore-karnataka',
            'bengaluru': 'bengaluru-bangalore-karnataka',
            'mumbai': 'mumbai-maharashtra',
            'delhi': 'delhi-new-delhi',
            'chennai': 'chennai-tamil-nadu',
            'pune': 'pune-maharashtra',
            'kolkata': 'kolkata-west-bengal',
            'ahmedabad': 'ahmedabad-gujarat',
            'jaipur': 'jaipur-rajasthan',
            'surat': 'surat-gujarat',
            'lucknow': 'lucknow-uttar-pradesh',
            'kanpur': 'kanpur-uttar-pradesh',
            'nagpur': 'nagpur-maharashtra',
            'indore': 'indore-madhya-pradesh',
            'thane': 'thane-maharashtra',
            'bhopal': 'bhopal-madhya-pradesh',
            'visakhapatnam': 'visakhapatnam-andhra-pradesh',
            'pimpri': 'pimpri-chinchwad-maharashtra',
            'patna': 'patna-bihar',
            'vadodara': 'vadodara-gujarat',
            'ghaziabad': 'ghaziabad-uttar-pradesh',
            'ludhiana': 'ludhiana-punjab',
            'agra': 'agra-uttar-pradesh',
            'nashik': 'nashik-maharashtra',
            'faridabad': 'faridabad-haryana',
            'meerut': 'meerut-uttar-pradesh',
            'rajkot': 'rajkot-gujarat',
            'kalyan': 'kalyan-dombivali-maharashtra',
            'vasai': 'vasai-virar-maharashtra',
            'varanasi': 'varanasi-uttar-pradesh',
            'srinagar': 'srinagar-jammu-kashmir',
            'aurangabad': 'aurangabad-maharashtra',
            'dhanbad': 'dhanbad-jharkhand',
            'amritsar': 'amritsar-punjab',
            'navi mumbai': 'navi-mumbai-maharashtra',
            'allahabad': 'allahabad-uttar-pradesh',
            'ranchi': 'ranchi-jharkhand',
            'howrah': 'howrah-west-bengal',
            'coimbatore': 'coimbatore-tamil-nadu',
            'jabalpur': 'jabalpur-madhya-pradesh',
            'gwalior': 'gwalior-madhya-pradesh',
            'vijayawada': 'vijayawada-andhra-pradesh',
            'jodhpur': 'jodhpur-rajasthan',
            'madurai': 'madurai-tamil-nadu',
            'raipur': 'raipur-chhattisgarh',
            'kota': 'kota-rajasthan',
            'guwahati': 'guwahati-assam',
            'chandigarh': 'chandigarh',
            'solapur': 'solapur-maharashtra',
            'hubli': 'hubli-dharwad-karnataka',
            'tiruchirappalli': 'tiruchirappalli-tamil-nadu',
            'bareilly': 'bareilly-uttar-pradesh',
            'mysore': 'mysore-karnataka',
            'tiruppur': 'tiruppur-tamil-nadu',
            'gurgaon': 'gurgaon-haryana',
            'aligarh': 'aligarh-uttar-pradesh',
            'jalandhar': 'jalandhar-punjab',
            'bhubaneswar': 'bhubaneswar-odisha',
            'salem': 'salem-tamil-nadu',
            'warangal': 'warangal-telangana',
            'mira': 'mira-bhayandar-maharashtra',
            'thiruvananthapuram': 'thiruvananthapuram-kerala',
            'bhiwandi': 'bhiwandi-maharashtra',
            'saharanpur': 'saharanpur-uttar-pradesh',
            'gorakhpur': 'gorakhpur-uttar-pradesh',
            'guntur': 'guntur-andhra-pradesh',
            'bikaner': 'bikaner-rajasthan',
            'amravati': 'amravati-maharashtra',
            'noida': 'noida-uttar-pradesh',
            'jamshedpur': 'jamshedpur-jharkhand',
            'bhilai': 'bhilai-chhattisgarh',
            'cuttack': 'cuttack-odisha',
            'firozabad': 'firozabad-uttar-pradesh',
            'kochi': 'kochi-kerala',
            'nellore': 'nellore-andhra-pradesh',
            'bhavnagar': 'bhavnagar-gujarat',
            'dehradun': 'dehradun-uttarakhand',
            'durgapur': 'durgapur-west-bengal',
            'asansol': 'asansol-west-bengal',
            'rourkela': 'rourkela-odisha',
            'nanded': 'nanded-maharashtra',
            'kolhapur': 'kolhapur-maharashtra',
            'ajmer': 'ajmer-rajasthan',
            'akola': 'akola-maharashtra',
            'gulbarga': 'gulbarga-karnataka',
            'jamnagar': 'jamnagar-gujarat',
            'ujjain': 'ujjain-madhya-pradesh',
            'loni': 'loni-uttar-pradesh',
            'siliguri': 'siliguri-west-bengal',
            'jhansi': 'jhansi-uttar-pradesh',
            'ulhasnagar': 'ulhasnagar-maharashtra',
            'jammu': 'jammu-jammu-kashmir',
            'sangli': 'sangli-miraj-kupwad-maharashtra',
            'mangalore': 'mangalore-karnataka',
            'erode': 'erode-tamil-nadu',
            'belgaum': 'belgaum-karnataka',
            'ambattur': 'ambattur-tamil-nadu',
            'tirunelveli': 'tirunelveli-tamil-nadu',
            'malegaon': 'malegaon-maharashtra',
            'gaya': 'gaya-bihar',
            'jalgaon': 'jalgaon-maharashtra',
            'udaipur': 'udaipur-rajasthan',
            'maheshtala': 'maheshtala-west-bengal'
        }

        # Clean and normalize the location
        location_clean = location.lower().strip()

        # Check if we have a specific mapping
        if location_clean in location_mappings:
            return location_mappings[location_clean]

        # If no specific mapping, create a basic slug
        return self.create_url_slug(location)

    def construct_job_url(self, job_title: str, company_name: str, location: str, job_id: str) -> str:
        """Construct foundit.in job URL from components"""
        try:
            # Create slugs for each component
            title_slug = self.create_url_slug(job_title)
            company_slug = self.create_url_slug(company_name)

            # Enhanced location handling to match foundit.in format
            location_slug = self.create_enhanced_location_slug(location)

            # Construct the URL path
            url_path = f"{title_slug}-{company_slug}-{location_slug}-{job_id}"

            # Get search parameters using improved extraction
            search_params_dict = self.extract_search_parameters()
            search_params = ""

            if search_params_dict:
                params = []
                if 'searchId' in search_params_dict:
                    params.append(f"searchId={search_params_dict['searchId']}")
                if 'child_search_id' in search_params_dict:
                    params.append(f"child_search_id={search_params_dict['child_search_id']}")

                if params:
                    search_params = "?" + "&".join(params)

            # Construct final URL
            job_url = f"https://www.foundit.in/job/{url_path}{search_params}"

            # Log debugging information
            self.log_url_construction_debug(job_title, company_name, location, job_id, job_url)

            # Validate the constructed URL
            if self.validate_constructed_url(job_url):
                logger.info(f"Successfully constructed valid job URL: {job_url}")
                return job_url
            else:
                logger.warning(f"Constructed URL failed validation: {job_url}")
                return job_url  # Return anyway, might still work

        except Exception as e:
            logger.error(f"Error constructing job URL: {e}")
            return None

    def extract_search_parameters(self) -> dict:
        """Extract search parameters from current page for URL construction"""
        try:
            # Check if driver is available
            if not hasattr(self, 'driver') or self.driver is None:
                # Generate default search parameters if no driver
                import uuid
                return {
                    'searchId': str(uuid.uuid4()),
                    'child_search_id': str(uuid.uuid4())
                }

            # Method 1: Extract from current URL
            current_url = self.driver.current_url
            from urllib.parse import urlparse, parse_qs
            parsed_url = urlparse(current_url)
            query_params = parse_qs(parsed_url.query)

            search_id = query_params.get('searchId', [None])[0]
            child_search_id = query_params.get('child_search_id', [None])[0]

            if search_id and child_search_id:
                return {
                    'searchId': search_id,
                    'child_search_id': child_search_id
                }

            # Method 2: Look for search parameters in page source or JavaScript
            try:
                page_source = self.driver.page_source
                import re

                # Look for searchId in page source
                search_id_match = re.search(r'searchId["\']?\s*[:=]\s*["\']?([a-f0-9-]{36})["\']?', page_source, re.IGNORECASE)
                child_search_id_match = re.search(r'child_search_id["\']?\s*[:=]\s*["\']?([a-f0-9-]{36})["\']?', page_source, re.IGNORECASE)

                if search_id_match and child_search_id_match:
                    return {
                        'searchId': search_id_match.group(1),
                        'child_search_id': child_search_id_match.group(1)
                    }
                elif search_id_match:
                    return {
                        'searchId': search_id_match.group(1)
                    }

            except Exception as e:
                logger.debug(f"Could not extract search parameters from page source: {e}")

            # Method 3: Generate default search parameters if none found
            import uuid
            return {
                'searchId': str(uuid.uuid4()),
                'child_search_id': str(uuid.uuid4())
            }

        except Exception as e:
            logger.debug(f"Error extracting search parameters: {e}")
            # Return default parameters as fallback
            import uuid
            return {
                'searchId': str(uuid.uuid4()),
                'child_search_id': str(uuid.uuid4())
            }

    def improve_job_id_extraction(self, card_element, job_title: str, company_name: str) -> Optional[str]:
        """Improved job ID extraction using multiple strategies"""
        try:
            # Strategy 1: Look for Apply button URLs which often contain job IDs
            apply_buttons = card_element.find_elements(By.CSS_SELECTOR, 'button[id*="apply"], a[href*="apply"], a[href*="job"]')
            for button in apply_buttons:
                href = button.get_attribute('href')
                onclick = button.get_attribute('onclick')

                if href:
                    import re
                    # Look for job ID in apply URLs
                    id_match = re.search(r'(\d{8,})', href)
                    if id_match:
                        job_id = id_match.group(1)
                        logger.debug(f"Found job ID via apply button href: {job_id}")
                        return job_id

                if onclick:
                    import re
                    id_match = re.search(r'(\d{8,})', onclick)
                    if id_match:
                        job_id = id_match.group(1)
                        logger.debug(f"Found job ID via apply button onclick: {job_id}")
                        return job_id

            # Strategy 2: Look for job ID in data attributes more thoroughly
            all_elements = card_element.find_elements(By.CSS_SELECTOR, '*')
            for elem in all_elements:
                # Check all data attributes
                attrs = self.driver.execute_script(
                    "var items = {}; for (index = 0; index < arguments[0].attributes.length; ++index) { items[arguments[0].attributes[index].name] = arguments[0].attributes[index].value }; return items;",
                    elem
                )

                for attr_name, attr_value in attrs.items():
                    if attr_name.startswith('data-') and attr_value:
                        import re
                        id_match = re.search(r'(\d{8,})', str(attr_value))
                        if id_match:
                            job_id = id_match.group(1)
                            logger.debug(f"Found job ID via {attr_name}: {job_id}")
                            return job_id

            # Strategy 3: Generate a pseudo job ID based on job data
            # This is a fallback when no real job ID is found
            import hashlib
            job_string = f"{job_title}-{company_name}".lower()
            hash_object = hashlib.md5(job_string.encode())
            pseudo_id = str(int(hash_object.hexdigest()[:8], 16))

            # Ensure it's at least 8 digits
            while len(pseudo_id) < 8:
                pseudo_id = "1" + pseudo_id

            logger.debug(f"Generated pseudo job ID: {pseudo_id}")
            return pseudo_id

        except Exception as e:
            logger.debug(f"Error in improved job ID extraction: {e}")
            return None

    def generate_pseudo_job_id(self, job_title: str, company_name: str) -> str:
        """Generate a pseudo job ID based on job data as fallback"""
        try:
            import hashlib
            job_string = f"{job_title}-{company_name}".lower()
            hash_object = hashlib.md5(job_string.encode())
            pseudo_id = str(int(hash_object.hexdigest()[:8], 16))

            # Ensure it's at least 8 digits
            while len(pseudo_id) < 8:
                pseudo_id = "1" + pseudo_id

            logger.debug(f"Generated pseudo job ID: {pseudo_id} for {job_title} at {company_name}")
            return pseudo_id
        except Exception as e:
            logger.debug(f"Error generating pseudo job ID: {e}")
            return "99999999"  # Default fallback

    def validate_constructed_url(self, url: str) -> bool:
        """Validate if the constructed URL follows the correct foundit.in pattern"""
        try:
            if not url:
                return False

            import re
            # Pattern for foundit.in job URLs
            pattern = r'^https://www\.foundit\.in/job/[a-z0-9-]+-[a-z0-9-]+-[a-z0-9-]+-[a-z0-9_]+(\?.*)?$'

            if re.match(pattern, url):
                logger.debug(f"URL validation passed: {url}")
                return True
            else:
                logger.debug(f"URL validation failed: {url}")
                return False

        except Exception as e:
            logger.debug(f"Error validating URL: {e}")
            return False

    def log_url_construction_debug(self, job_title: str, company_name: str, location: str, job_id: str, final_url: str):
        """Log detailed information about URL construction for debugging"""
        logger.debug("=== URL Construction Debug ===")
        logger.debug(f"Job Title: '{job_title}' -> Slug: '{self.create_url_slug(job_title)}'")
        logger.debug(f"Company: '{company_name}' -> Slug: '{self.create_url_slug(company_name)}'")
        logger.debug(f"Location: '{location}' -> Slug: '{self.create_url_slug(location)}'")
        logger.debug(f"Job ID: '{job_id}'")
        logger.debug(f"Final URL: '{final_url}'")
        logger.debug(f"URL Valid: {self.validate_constructed_url(final_url)}")
        logger.debug("=== End Debug ===")

    def scrape_detailed_job_info(self, job_data: JobData, card_element=None) -> JobData:
        """Navigate to job detail page and extract comprehensive information"""
        job_detail_url = job_data.job_url

        # Prioritize click-through navigation for jobs marked as needing navigation
        if job_detail_url == "NEEDS_NAVIGATION" and card_element:
            logger.info(f"Attempting click-through navigation for: {job_data.title}")
            job_detail_url = self.try_navigate_to_job_detail(card_element)
            if job_detail_url:
                job_data.job_url = job_detail_url
                logger.info(f"Successfully navigated to: {job_detail_url}")
            else:
                logger.warning(f"Click-through navigation failed for: {job_data.title}")
                # As a last resort, try URL construction with pseudo ID
                pseudo_id = self.generate_pseudo_job_id(job_data.title, job_data.company_name)
                constructed_url = self.construct_job_url(
                    job_data.title,
                    job_data.company_name,
                    job_data.location or "india",
                    pseudo_id
                )
                job_data.job_url = constructed_url
                job_data.job_id = pseudo_id
                logger.info(f"Using constructed URL as fallback: {constructed_url}")
                return job_data

        # If no valid URL found, try clicking on the card to navigate
        elif (not job_detail_url or '/job/' not in job_detail_url) and card_element:
            logger.info(f"No direct URL found, trying to navigate by clicking for: {job_data.title}")
            job_detail_url = self.try_navigate_to_job_detail(card_element)
            if job_detail_url:
                job_data.job_url = job_detail_url

        if not job_detail_url or '/job/' not in job_detail_url:
            logger.warning(f"No valid job detail URL for job: {job_data.title}")
            return job_data

        try:
            logger.info(f"Navigating to job detail page: {job_detail_url}")
            self.driver.get(job_detail_url)
            self.random_delay(2, 4)

            # Wait for job detail page to load
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '#jobDetailContainer, .job-description-content, h1'))
                )
            except TimeoutException:
                logger.warning("Job detail page did not load properly")
                return job_data

            # Extract detailed job information

            # Job title (more precise)
            try:
                title_elem = self.driver.find_element(By.CSS_SELECTOR, 'h1')
                if title_elem.text.strip():
                    job_data.title = title_elem.text.strip()
            except NoSuchElementException:
                pass

            # Company name and logo
            try:
                company_elem = self.driver.find_element(By.CSS_SELECTOR, 'a[href*="jobs-career"], a[target="_blank"]')
                if company_elem.text.strip():
                    job_data.company_name = company_elem.text.strip()
            except NoSuchElementException:
                pass

            # Company logo
            try:
                logo_elem = self.driver.find_element(By.CSS_SELECTOR, 'img[alt*="logo"], img[src*="logo"]')
                job_data.company_description = f"Logo: {logo_elem.get_attribute('src')}"
            except NoSuchElementException:
                pass

            # Experience
            try:
                exp_elems = self.driver.find_elements(By.XPATH, "//span[contains(text(), 'Years') or contains(text(), 'yrs') or contains(text(), '-')]")
                for elem in exp_elems:
                    text = elem.text.strip()
                    if any(keyword in text.lower() for keyword in ['year', 'yrs', 'experience']) or '-' in text:
                        job_data.experience = text
                        break
            except Exception:
                pass

            # Location
            try:
                # Try multiple approaches for location
                location_found = False

                # Method 1: Look for location links
                location_elems = self.driver.find_elements(By.CSS_SELECTOR, 'a[href*="jobs-in-"]')
                for elem in location_elems:
                    if elem.text.strip():
                        job_data.location = elem.text.strip()
                        location_found = True
                        break

                # Method 2: Look for common city names
                if not location_found:
                    city_elements = self.driver.find_elements(By.XPATH, "//div[contains(text(), 'Bengaluru') or contains(text(), 'Mumbai') or contains(text(), 'Delhi') or contains(text(), 'Pune') or contains(text(), 'Chennai') or contains(text(), 'Hyderabad')]")
                    if city_elements:
                        job_data.location = city_elements[0].text.strip()
                        location_found = True

            except Exception:
                pass

            # Job description
            try:
                desc_elem = self.driver.find_element(By.CSS_SELECTOR, '#jobDescription .job-description-content, .break-words')
                job_data.job_description = desc_elem.text.strip()
            except NoSuchElementException:
                # Try alternative selector
                try:
                    desc_elem = self.driver.find_element(By.CSS_SELECTOR, '.job-description-content')
                    job_data.job_description = desc_elem.text.strip()
                except NoSuchElementException:
                    pass

            # Skills
            try:
                skill_elements = self.driver.find_elements(By.CSS_SELECTOR, '#skillSectionNew label, .bg-surface-secondary label')
                skills = []
                for skill_elem in skill_elements:
                    skill_text = skill_elem.text.strip()
                    if skill_text and skill_text not in skills:
                        skills.append(skill_text)
                job_data.skills = skills
            except NoSuchElementException:
                pass

            # Posted date and Job ID
            try:
                date_elem = self.driver.find_element(By.XPATH, "//p[contains(text(), 'Date Posted:')]")
                job_data.posted_date = date_elem.text.replace('Date Posted:', '').strip()
            except NoSuchElementException:
                pass

            try:
                job_id_elem = self.driver.find_element(By.XPATH, "//p[contains(text(), 'Job ID:')]")
                job_data.job_id = job_id_elem.text.replace('Job ID:', '').strip()
            except NoSuchElementException:
                pass

            # Industry, Role, Function
            try:
                more_info_section = self.driver.find_element(By.XPATH, "//h2[contains(text(), 'More Info')]/following-sibling::div")
                info_items = more_info_section.find_elements(By.CSS_SELECTOR, 'p')

                for item in info_items:
                    text = item.text.strip()
                    if 'Role:' in text:
                        job_data.roles = text.replace('Role:', '').strip()
                    elif 'Industry:' in text:
                        job_data.industry = text.replace('Industry:', '').strip()
                    elif 'Function:' in text:
                        job_data.function = text.replace('Function:', '').strip()
            except NoSuchElementException:
                pass

            # Company description from About Company section
            try:
                company_desc_elem = self.driver.find_element(By.XPATH, "//h2[contains(text(), 'About Company')]/following-sibling::div//p")
                if company_desc_elem.text.strip():
                    job_data.company_description = company_desc_elem.text.strip()
            except NoSuchElementException:
                pass

            logger.info(f"Successfully extracted detailed info for: {job_data.title}")

        except Exception as e:
            logger.error(f"Error scraping detailed job info from {job_data.job_url}: {e}")

        return job_data
    

    
    def go_to_next_page(self) -> bool:
        """Navigate to next page of search results"""
        try:
            next_button = self.driver.find_element(By.CSS_SELECTOR, self.selectors['next_page'])
            if next_button.is_displayed() and next_button.is_enabled():
                next_button.click()
                self.random_delay(3, 5)
                
                # Wait for new page to load
                self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards'])))
                logger.info("Navigated to next page")
                return True
            else:
                logger.info("No next page button found or not clickable")
                return False
                
        except NoSuchElementException:
            logger.info("Next page button not found")
            return False
        except Exception as e:
            logger.warning(f"Error navigating to next page: {e}")
            return False
    
    def scrape_jobs(self, job_title: str, location: str, num_jobs: int = 5) -> Dict[str, List[Dict]]:
        """Main scraping function with improved error handling"""
        driver = None
        jobs = []
        scraped_count = 0
        page_num = 1
        
        try:
            logger.info(f"Starting scrape for '{job_title}' in '{location}' - Target: {num_jobs} jobs")
            
            # Setup driver with retry logic
            try:
                driver = self.setup_driver()
                self.driver = driver
            except Exception as e:
                logger.error(f"Failed to setup driver: {e}")
                return {'scraped_jobs': [], 'total_scraped': 0, 'error': f"Driver setup failed: {str(e)}"}
            
            # Fill search form
            try:
                if not self.fill_search_form(job_title, location):
                    raise Exception("Failed to submit search form")
                logger.info("Search form submitted successfully, proceeding to extract jobs")
            except Exception as e:
                logger.error(f"Error during search form submission: {e}")
                logger.info(f"Current URL: {self.driver.current_url}")
                logger.info(f"Page title: {self.driver.title}")
                raise Exception(f"Failed to submit search form: {str(e)}")
            
            while scraped_count < num_jobs and page_num <= 10:  # Limit to 10 pages max
                logger.info(f"Scraping page {page_num}...")

                # Extract job data directly from current page (new approach)
                page_jobs, job_cards = self.extract_job_data_from_search_results()

                if not page_jobs:
                    # Check if this is a "no results" page vs a loading issue
                    page_title = self.driver.title
                    if ': 0 ' in page_title or 'no jobs' in page_title.lower() or '0 job vacancies' in page_title.lower():
                        logger.info("Search completed successfully - no jobs found for the given criteria")
                        break
                    else:
                        logger.warning("No jobs found on current page - might be a loading issue")
                        break

                logger.info(f"Found {len(page_jobs)} jobs on page {page_num}")

                # Add jobs to results with detailed scraping
                for i, job_data in enumerate(page_jobs):
                    if scraped_count >= num_jobs:
                        break

                    try:
                        if job_data.title:  # Only add if we got meaningful data
                            # Get the corresponding card element for navigation if needed
                            card_element = None
                            try:
                                if i < len(job_cards):
                                    card_element = job_cards[i]
                            except:
                                pass

                            # Scrape detailed information from job detail page
                            detailed_job_data = self.scrape_detailed_job_info(job_data, card_element)
                            jobs.append(asdict(detailed_job_data))

                            if detailed_job_data.job_url and '/job/' in detailed_job_data.job_url:
                                logger.info(f"Scraped detailed job {scraped_count + 1}/{num_jobs}: {detailed_job_data.title}")
                            else:
                                logger.info(f"Scraped basic job {scraped_count + 1}/{num_jobs}: {detailed_job_data.title}")

                            scraped_count += 1

                            # Add delay between job detail page requests
                            if scraped_count < num_jobs:
                                self.random_delay(1, 2)

                    except Exception as e:
                        logger.error(f"Error processing job: {e}")
                        continue

                # Try to navigate to next page
                if scraped_count < num_jobs:
                    if not self.go_to_next_page():
                        logger.info("No more pages available")
                        break
                    page_num += 1
                else:
                    break
            
            logger.info(f"Scraping completed. Total jobs scraped: {len(jobs)}")

            # Provide informative response based on results
            result = {'scraped_jobs': jobs, 'total_scraped': len(jobs), 'requested': num_jobs}

            if len(jobs) == 0:
                # Check if this was a "no results" case
                page_title = self.driver.title if self.driver else ""
                if ': 0 ' in page_title or 'no jobs' in page_title.lower() or '0 job vacancies' in page_title.lower():
                    result['message'] = f"No jobs found for '{job_title}' in '{location}' on Foundit. Try different search terms or location."
                    result['suggestion'] = "Try searching for 'Data Scientist' in 'Bangalore' or 'Mumbai' instead of 'India'"
                else:
                    result['message'] = "No jobs could be extracted. This might be due to page loading issues or changed website structure."

            return result
            
        except Exception as e:
            logger.error(f"Fatal error during scraping: {e}")
            return {'scraped_jobs': jobs, 'total_scraped': len(jobs), 'error': str(e)}
        
        finally:
            if driver:
                try:
                    driver.quit()
                    logger.info("Driver closed")
                except Exception as e:
                    logger.warning(f"Error closing driver: {e}")

# API Endpoints
@app.get("/")
def root():
    return {
        "message": "Foundit Job Scraper API", 
        "version": "2.0",
        "status": "running",
        "timestamp": time.time(),
        "endpoints": {
            "GET /scrape_foundit": "Scrape jobs (GET)",
            "POST /scrape_foundit": "Scrape jobs (POST)",
            "GET /health": "Health check"
        }
    }

# Request model for POST endpoint
class FounditRequest(BaseModel):
    job_title: str
    location: str
    num_jobs: int = 5

@app.get("/scrape_foundit")
def scrape_foundit_api(
    job_title: str = Query(..., description="Job title to search for"),
    location: str = Query(..., description="Location to search in"),
    num_jobs: int = Query(5, ge=1, le=50, description="Number of jobs to scrape (1-50)")
):
    """
    Scrape job listings from Foundit (GET)
    """
    try:
        scraper = FounditScraper(headless=False)
        result = scraper.scrape_jobs(job_title, location, num_jobs)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"API error: {e}")
        raise HTTPException(status_code=500, detail=f"Scraping failed: {str(e)}")

@app.post("/scrape_foundit")
def scrape_foundit_post_api(request: FounditRequest):
    """
    Scrape job listings from Foundit (POST)
    """
    try:
        scraper = FounditScraper(headless=False)
        result = scraper.scrape_jobs(request.job_title, request.location, request.num_jobs)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"API error: {e}")
        raise HTTPException(status_code=500, detail=f"Scraping failed: {str(e)}")

@app.get("/health")
def health_check():
    return {
        "status": "healthy", 
        "timestamp": time.time(),
        "service": "Foundit Scraper",
        "version": "2.0",
        "endpoints": {
            "GET /scrape_foundit": "Scrape jobs (GET)",
            "POST /scrape_foundit": "Scrape jobs (POST)",
            "GET /health": "Health check"
        }
    }

@app.options("/scrape_foundit")
async def options_scrape_foundit():
    return {"message": "OK"}

@app.options("/scrape_foundit")
async def options_scrape_foundit_post():
    return {"message": "OK"}

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "serve":
        uvicorn.run(app, host="0.0.0.0", port=8003, reload=True)
    else:
        # Command line usage
        job_title = sys.argv[1] if len(sys.argv) > 1 else "Data Scientist"
        location = sys.argv[2] if len(sys.argv) > 2 else "India"
        num_jobs = int(sys.argv[3]) if len(sys.argv) > 3 else 5
        
        scraper = FounditScraper(headless=False)  # Visible browser for debugging
        result = scraper.scrape_jobs(job_title, location, num_jobs)
        print(json.dumps(result, indent=2)) 