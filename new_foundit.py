import time
import logging
import random
import uuid
from typing import Dict, List
from dataclasses import dataclass, asdict
from urllib.parse import quote_plus
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException, ElementClickInterceptedException
from fastapi import FastAPI, HTTPException, Body
from fastapi.responses import JSONResponse
import uvicorn
import undetected_chromedriver as uc
from fastapi.middleware.cors import CORSMiddleware

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = FastAPI(title="Foundit Job Scraper", version="3.0")

# Define allowed origins including ngrok URLs
allowed_origins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://localhost:3001",
    "https://localhost:3001",
    "https://0305-103-247-7-151.ngrok-free.app",
    "https://7ea9-103-247-7-151.ngrok-free.app",
    "https://65d0-202-148-58-240.ngrok-free.app",
    "https://ffb46ce19203.ngrok-free.app",
    "*"  # Allow all origins for development
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,  # Set to False when using "*" or multiple origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

@dataclass
class JobData:
    """Data structure for job information"""
    job_id: str = ""
    title: str = ""
    company_name: str = ""
    location: str = ""
    experience: str = ""
    salary: str = ""
    job_description: str = ""
    skills: List[str] = None
    posted_date: str = ""
    job_url: str = ""
    company_description: str = ""
    roles: str = ""
    industry: str = ""
    function: str = ""
    
    def __post_init__(self):
        if self.skills is None:
            self.skills = []

class FounditScraper:
    """Foundit scraper with direct URL navigation and click-through job extraction"""
    
    def __init__(self, headless: bool = True, timeout: int = 60):
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self.wait = None
        
        # Selectors for job cards and job detail pages
        self.selectors = {
            'cookie_banner': '#cookieBanner',
            'accept_cookies': '#acceptAll',
            'job_cards': 'div[data-index]',  # Job cards with data-index
            'job_title': 'h3[title], h3.text-base.font-bold',
            'company_name': 'span p, .text-sm.font-normal p',
            'experience_label': 'label',  # Experience labels
            'location_label': 'label',  # Location labels  
            'posted_date': 'label.text-xxs',  # Posted date labels
            'salary_label': 'label',  # Salary labels
            'apply_button': '#applyBtn, button:contains("Apply")',
            'save_button': 'button:contains("Save")',
            'company_logo': 'img[alt]',
            'next_page': '.pagination-next a, .next-page a, button:contains("Next")',
            # Job detail page selectors
            'job_detail_title': 'h1',
            'job_detail_company': 'a[href*="jobs-career"], a[target="_blank"]',
            'job_detail_description': '#jobDescription .job-description-content, .break-words',
            'job_detail_skills': '#skillSectionNew label, .bg-surface-secondary label',
            'job_detail_posted_date': 'p:contains("Date Posted:")',
            'job_detail_job_id': 'p:contains("Job ID:")',
            'job_detail_role': 'p:contains("Role:")',
            'job_detail_industry': 'p:contains("Industry:")',
            'job_detail_function': 'p:contains("Function:")'
        }
    
    def setup_driver(self) -> uc.Chrome:
        """Setup Chrome driver with optimized options"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                options = uc.ChromeOptions()
                
                # Essential options for scraping
                if self.headless:
                    options.add_argument('--headless=new')
                    options.add_argument('--window-size=1920,1080')
                
                # Anti-detection options
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-blink-features=AutomationControlled')
                
                # Performance options
                options.add_argument('--disable-images')
                options.add_argument('--disable-extensions')
                options.add_argument('--disable-plugins')
                options.add_argument('--disable-gpu')
                options.add_argument('--disable-web-security')
                options.add_argument('--disable-features=VizDisplayCompositor')
                options.add_argument('--disable-logging')
                options.add_argument('--disable-dev-tools')
                options.add_argument('--no-first-run')
                options.add_argument('--no-default-browser-check')
                options.add_argument('--disable-default-apps')
                options.add_argument('--disable-popup-blocking')
                options.add_argument('--ignore-certificate-errors')
                options.add_argument('--ignore-ssl-errors')
                options.add_argument('--ignore-certificate-errors-spki-list')
                
                # User agent rotation
                user_agents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ]
                options.add_argument(f'--user-agent={random.choice(user_agents)}')
                
                logger.info(f"Attempting to initialize Chrome driver (attempt {attempt + 1}/{max_retries})")
                
                self.driver = uc.Chrome(options=options)
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                self.wait = WebDriverWait(self.driver, self.timeout)
                
                logger.info("Chrome driver initialized successfully")
                return self.driver
                
            except Exception as e:
                logger.error(f"Failed to setup driver (attempt {attempt + 1}/{max_retries}): {e}")
                if self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None
                
                if attempt == max_retries - 1:
                    raise WebDriverException(f"Driver setup failed after {max_retries} attempts: {e}")
                
                # Wait before retry
                time.sleep(2)
        
        raise WebDriverException("Failed to initialize Chrome driver")
    
    def random_delay(self, min_seconds: float = 1, max_seconds: float = 3):
        """Add random delay to avoid detection"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def create_search_url(self, job_title: str, location: str) -> str:
        """Create direct search URL for foundit.in"""
        try:
            # Generate random UUIDs for search parameters
            search_id = str(uuid.uuid4())
            child_search_id = str(uuid.uuid4())
            
            # URL encode the parameters
            encoded_job_title = quote_plus(job_title)
            encoded_location = quote_plus(location)
            
            # Create URL slug for the path (lowercase, replace spaces with hyphens)
            job_slug = job_title.lower().replace(' ', '-').replace('+', '-')
            location_slug = location.lower().replace(' ', '-').replace('/', '-')
            
            # Construct the search URL
            search_url = (
                f"https://www.foundit.in/search/{job_slug}-jobs-in-{location_slug}"
                f"?query={encoded_job_title}"
                f"&locations={encoded_location}"
                f"&queryDerived=true"
                f"&searchId={search_id}"
                f"&child_search_id={child_search_id}"
            )
            
            logger.info(f"Created search URL: {search_url}")
            return search_url
            
        except Exception as e:
            logger.error(f"Error creating search URL: {e}")
            # Fallback to basic URL
            return f"https://www.foundit.in/search/{job_title.lower().replace(' ', '-')}-jobs"
    
    def navigate_to_search_results(self, job_title: str, location: str) -> bool:
        """Navigate directly to search results page"""
        try:
            search_url = self.create_search_url(job_title, location)
            logger.info(f"Navigating to search results: {search_url}")
            
            self.driver.get(search_url)
            self.random_delay(2, 4)
            
            # Handle cookies if present
            self.handle_cookies()
            
            # Wait for job cards to load
            try:
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards']))
                )
                logger.info("Search results page loaded successfully")
                return True
            except TimeoutException:
                # Check if it's a no results page
                page_title = self.driver.title
                if ': 0 ' in page_title or 'no jobs' in page_title.lower():
                    logger.info("No jobs found for the search criteria")
                    return True
                else:
                    logger.warning("Search results page did not load properly")
                    return False
                    
        except Exception as e:
            logger.error(f"Error navigating to search results: {e}")
            return False
    
    def handle_cookies(self):
        """Handle cookie consent banner if present"""
        try:
            # Check if cookie banner exists
            cookie_banner = self.driver.find_element(By.CSS_SELECTOR, self.selectors['cookie_banner'])
            if cookie_banner.is_displayed():
                logger.info("Cookie banner found, accepting cookies...")
                accept_button = self.driver.find_element(By.CSS_SELECTOR, self.selectors['accept_cookies'])
                accept_button.click()
                self.random_delay(1, 2)
                logger.info("Cookies accepted successfully")
        except NoSuchElementException:
            logger.info("No cookie banner found")
        except Exception as e:
            logger.warning(f"Error handling cookies: {e}")

    def extract_job_cards_data(self) -> List[JobData]:
        """Extract basic job data from search results page"""
        jobs = []
        try:
            # Find all job cards
            job_cards = self.driver.find_elements(By.CSS_SELECTOR, self.selectors['job_cards'])
            logger.info(f"Found {len(job_cards)} job cards on current page")

            for index, card in enumerate(job_cards):
                try:
                    job = JobData()
                    job.job_id = str(index + 1)  # Use index as temporary ID

                    # Extract job title
                    try:
                        title_elem = card.find_element(By.CSS_SELECTOR, self.selectors['job_title'])
                        job.title = title_elem.get_attribute('title') or title_elem.text.strip()
                    except NoSuchElementException:
                        continue  # Skip if no title found

                    # Extract company name
                    try:
                        company_elem = card.find_element(By.CSS_SELECTOR, self.selectors['company_name'])
                        job.company_name = company_elem.text.strip()
                    except NoSuchElementException:
                        pass

                    # Extract experience, location, salary, posted date from labels
                    labels = card.find_elements(By.TAG_NAME, 'label')
                    for label in labels:
                        text = label.text.strip()
                        if 'yrs' in text.lower() or 'year' in text.lower():
                            job.experience = text
                        elif any(city in text.lower() for city in ['bengaluru', 'bangalore', 'mumbai', 'delhi', 'chennai', 'hyderabad', 'pune', 'gurgaon', 'noida']):
                            job.location = text
                        elif '₹' in text or 'lpa' in text.lower():
                            job.salary = text
                        elif 'posted' in text.lower():
                            job.posted_date = text.replace('Posted', '').strip()

                    # Extract company logo
                    try:
                        logo_elem = card.find_element(By.CSS_SELECTOR, self.selectors['company_logo'])
                        job.company_description = f"Logo: {logo_elem.get_attribute('src')}"
                    except NoSuchElementException:
                        pass

                    # Set current page URL as job URL (will be updated when clicking through)
                    job.job_url = self.driver.current_url

                    if job.title and job.company_name:
                        jobs.append(job)
                        logger.debug(f"Extracted basic job data: {job.title} at {job.company_name}")

                except Exception as e:
                    logger.warning(f"Error extracting data from job card {index}: {e}")
                    continue

            logger.info(f"Successfully extracted {len(jobs)} jobs from search results")
            return jobs

        except Exception as e:
            logger.error(f"Error extracting job cards data: {e}")
            return []

    def click_through_to_job_detail(self, job_data: JobData, card_index: int) -> JobData:
        """Click on job card to navigate to detailed job page"""
        try:
            # Store current URL to return if needed
            search_url = self.driver.current_url
            logger.info(f"Attempting to click through for job: {job_data.title} (index: {card_index})")

            # Find the job card again by index
            job_cards = self.driver.find_elements(By.CSS_SELECTOR, self.selectors['job_cards'])
            if card_index >= len(job_cards):
                logger.warning(f"Job card index {card_index} not found")
                return job_data

            card = job_cards[card_index]

            # First, try to find direct job URLs in data attributes or hidden elements
            direct_url_found = False
            try:
                # Check for data attributes that might contain job URLs
                url_attributes = ['data-href', 'data-url', 'data-job-url', 'data-link', 'href']
                for attr in url_attributes:
                    url_value = card.get_attribute(attr)
                    if url_value and ('job' in url_value.lower() or 'vacancy' in url_value.lower()):
                        if not url_value.startswith('http'):
                            url_value = 'https://www.foundit.in' + url_value
                        logger.info(f"Found direct job URL in {attr}: {url_value}")
                        self.driver.get(url_value)
                        self.random_delay(2, 3)
                        if self.driver.current_url != search_url:
                            job_data.job_url = self.driver.current_url
                            direct_url_found = True
                            clicked = True
                            break

                # Also check for hidden anchor tags or links within the card
                if not direct_url_found:
                    hidden_links = card.find_elements(By.CSS_SELECTOR, 'a[href*="job"], a[href*="vacancy"], a[style*="display:none"], a[hidden]')
                    for link in hidden_links:
                        href = link.get_attribute('href')
                        if href and href != search_url:
                            logger.info(f"Found hidden job link: {href}")
                            self.driver.get(href)
                            self.random_delay(2, 3)
                            if self.driver.current_url != search_url:
                                job_data.job_url = self.driver.current_url
                                direct_url_found = True
                                clicked = True
                                break
            except Exception as e:
                logger.debug(f"Error checking for direct URLs: {e}")

            if not direct_url_found:
                # Fallback to click strategies
                clickable_strategies = [
                    # Try the job card container itself (has cursor-pointer class)
                    {
                        'name': 'job_card_container',
                        'selector': '.cursor-pointer',
                        'element': card
                    },
                    # Try the job title specifically
                    {
                        'name': 'job_title',
                        'selector': self.selectors['job_title'],
                        'element': card
                    },
                    # Try any anchor tag within the card
                    {
                        'name': 'anchor_link',
                        'selector': 'a',
                        'element': card
                    },
                    # Try the company name area
                    {
                        'name': 'company_area',
                        'selector': 'span p',
                        'element': card
                    },
                    # Try clicking the card itself directly
                    {
                        'name': 'card_direct',
                        'selector': None,
                        'element': card
                    }
                ]

            clicked = False
            for strategy in clickable_strategies:
                try:
                    if strategy['selector']:
                        # Find element within the card
                        clickable = strategy['element'].find_element(By.CSS_SELECTOR, strategy['selector'])
                    else:
                        # Use the card element directly
                        clickable = strategy['element']

                    if clickable.is_displayed():
                        logger.info(f"Trying click strategy: {strategy['name']}")

                        # Scroll to element to ensure it's visible
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", clickable)
                        self.random_delay(1, 2)

                        # Try clicking with multiple methods including JavaScript event simulation
                        click_methods = [
                            # Method 1: Standard Selenium click
                            lambda: clickable.click(),
                            # Method 2: JavaScript click
                            lambda: self.driver.execute_script("arguments[0].click();", clickable),
                            # Method 3: Dispatch mouse events (more comprehensive)
                            lambda: self.driver.execute_script("""
                                var element = arguments[0];
                                var events = ['mousedown', 'mouseup', 'click'];
                                events.forEach(function(eventType) {
                                    var event = new MouseEvent(eventType, {
                                        view: window,
                                        bubbles: true,
                                        cancelable: true,
                                        clientX: element.getBoundingClientRect().left + element.offsetWidth / 2,
                                        clientY: element.getBoundingClientRect().top + element.offsetHeight / 2
                                    });
                                    element.dispatchEvent(event);
                                });
                            """, clickable),
                            # Method 4: Focus and trigger events
                            lambda: self.driver.execute_script("""
                                var element = arguments[0];
                                element.focus();
                                element.click();
                                // Also try triggering any potential React/Vue click handlers
                                if (element._reactInternalFiber || element.__reactInternalInstance) {
                                    var clickEvent = new Event('click', { bubbles: true });
                                    element.dispatchEvent(clickEvent);
                                }
                            """, clickable),
                            # Method 5: Try to find and trigger any onclick handlers
                            lambda: self.driver.execute_script("""
                                var element = arguments[0];
                                // Try to trigger any attached event listeners
                                var clickEvent = new MouseEvent('click', {
                                    view: window,
                                    bubbles: true,
                                    cancelable: true
                                });
                                element.dispatchEvent(clickEvent);

                                // Also check parent elements for click handlers
                                var parent = element.parentElement;
                                while (parent && parent !== document.body) {
                                    parent.dispatchEvent(clickEvent);
                                    parent = parent.parentElement;
                                }
                            """, clickable),
                            # Method 6: Try keyboard navigation (Tab + Enter)
                            lambda: self._try_keyboard_navigation(clickable),
                            # Method 7: Try double-click
                            lambda: self._try_double_click(clickable),
                            # Method 8: Try to trigger navigation programmatically
                            lambda: self._try_programmatic_navigation(clickable, card_index)
                        ]

                        # Try all click methods, don't break after first success
                        for i, click_method in enumerate(click_methods):
                            try:
                                click_method()
                                logger.info(f"Click method {i+1} executed for strategy: {strategy['name']}")
                                # Wait a bit after each click attempt
                                self.random_delay(0.5, 1)
                            except Exception as click_error:
                                logger.debug(f"Click method {i+1} failed: {click_error}")
                                continue

                        # Check for navigation immediately after all click methods
                        navigation_detected = False
                        max_wait_attempts = 3  # Reduced from 5 to speed up

                        for wait_attempt in range(max_wait_attempts):
                            self.random_delay(1, 2)  # Reduced delay
                            current_url = self.driver.current_url

                            # Check if URL changed to a job detail page
                            job_detail_indicators = ['/job/', '/jobs/', '/job-detail', '/vacancy', '/position', '/view/', '/details/']
                            url_changed = current_url != search_url
                            is_job_detail = any(indicator in current_url.lower() for indicator in job_detail_indicators)

                            # Also check for significant URL changes (new parameters, different path)
                            url_significantly_different = (
                                len(current_url) > len(search_url) + 20 or
                                current_url.count('/') > search_url.count('/') or
                                'jobId' in current_url.lower() or
                                'jid' in current_url.lower() or
                                current_url != search_url  # Any URL change might be significant
                            )

                            logger.debug(f"Navigation check {wait_attempt + 1}/{max_wait_attempts}: {current_url}")

                            if url_changed and (is_job_detail or url_significantly_different):
                                logger.info(f"Successfully navigated to job detail page: {current_url}")
                                job_data.job_url = current_url
                                navigation_detected = True
                                clicked = True
                                break
                            elif current_url != search_url:
                                logger.debug(f"URL changed but not to job detail: {current_url}")
                                # Continue waiting, might be intermediate navigation
                            else:
                                logger.debug(f"No URL change detected yet (attempt {wait_attempt + 1}/{max_wait_attempts})")

                        if navigation_detected:
                            logger.info(f"Navigation successful with strategy: {strategy['name']}")
                            break
                        else:
                            logger.debug(f"Strategy {strategy['name']} did not navigate to job detail page after all attempts")
                            # Continue to next strategy

                except NoSuchElementException:
                    logger.debug(f"Element not found for strategy: {strategy['name']}")
                    continue
                except Exception as e:
                    logger.debug(f"Error with strategy {strategy['name']}: {e}")
                    continue

            if clicked:
                # Extract detailed information from job detail page
                logger.info(f"Extracting detailed job information for: {job_data.title}")
                job_data = self.extract_job_detail_data(job_data)

                # Navigate back to search results for next job
                logger.info("Navigating back to search results")
                self.driver.get(search_url)
                self.random_delay(2, 3)

                # Wait for search results to reload
                try:
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards']))
                    )
                    logger.info("Successfully returned to search results")
                except TimeoutException:
                    logger.warning("Search results page did not reload properly")
            else:
                # If clicking failed, try to extract any available job URLs from the page source
                logger.warning(f"Could not click through to job detail for: {job_data.title}")
                logger.info("Attempting to extract job URL from page source or data attributes")

                try:
                    # Try to find job URLs in the page source
                    page_source = self.driver.page_source

                    # Look for job URLs in the HTML
                    import re
                    job_url_patterns = [
                        r'href="([^"]*(?:job|vacancy|position)[^"]*)"',
                        r'data-href="([^"]*)"',
                        r'data-url="([^"]*)"',
                        r'data-job-url="([^"]*)"',
                        r'onclick="[^"]*(?:location\.href|window\.open)\s*=\s*[\'"]([^\'"]*)[\'"]'
                    ]

                    for pattern in job_url_patterns:
                        matches = re.findall(pattern, page_source, re.IGNORECASE)
                        if matches:
                            # Filter matches that look like job URLs
                            for match in matches:
                                if any(indicator in match.lower() for indicator in ['/job/', '/jobs/', '/vacancy', '/position']):
                                    if not match.startswith('http'):
                                        match = 'https://www.foundit.in' + match
                                    job_data.job_url = match
                                    logger.info(f"Found potential job URL in page source: {match}")
                                    break
                            if job_data.job_url != search_url:
                                break

                    # Try to extract job ID from card data attributes
                    try:
                        job_id_attrs = ['data-job-id', 'data-id', 'data-jid', 'id']
                        for attr in job_id_attrs:
                            job_id = card.get_attribute(attr)
                            if job_id and job_id.strip():
                                job_data.job_id = job_id.strip()
                                logger.info(f"Found job ID from attribute {attr}: {job_id}")
                                break
                    except Exception as e:
                        logger.debug(f"Error extracting job ID from attributes: {e}")

                except Exception as e:
                    logger.debug(f"Error extracting job URL from page source: {e}")

            return job_data

        except Exception as e:
            logger.error(f"Error in click-through navigation: {e}")
            # Ensure we're back on search results page
            try:
                if self.driver.current_url != search_url:
                    self.driver.get(search_url)
                    self.random_delay(2, 3)
            except:
                pass
            return job_data

    def _try_keyboard_navigation(self, element):
        """Try keyboard navigation (Tab + Enter) to activate element"""
        try:
            from selenium.webdriver.common.keys import Keys
            element.send_keys(Keys.TAB)
            self.random_delay(0.5, 1)
            element.send_keys(Keys.ENTER)
            logger.debug("Keyboard navigation attempted")
        except Exception as e:
            logger.debug(f"Keyboard navigation failed: {e}")
            raise e

    def _try_double_click(self, element):
        """Try double-click on element"""
        try:
            from selenium.webdriver.common.action_chains import ActionChains
            actions = ActionChains(self.driver)
            actions.double_click(element).perform()
            logger.debug("Double-click attempted")
        except Exception as e:
            logger.debug(f"Double-click failed: {e}")
            raise e

    def _try_programmatic_navigation(self, element, card_index):
        """Try to navigate programmatically by constructing job URLs"""
        try:
            # Try to construct job URL based on common patterns
            current_url = self.driver.current_url
            base_url = "https://www.foundit.in"

            # Method 1: Try to find job ID in the card's HTML
            card_html = element.get_attribute('outerHTML')
            import re

            # Look for job IDs in various formats
            job_id_patterns = [
                r'data-job-id="([^"]+)"',
                r'data-id="([^"]+)"',
                r'jobId["\']?\s*:\s*["\']?([^"\']+)',
                r'id["\']?\s*:\s*["\']?([^"\']+)',
                r'/job/([^/\s"\']+)',
                r'jid=([^&\s"\']+)'
            ]

            for pattern in job_id_patterns:
                matches = re.findall(pattern, card_html, re.IGNORECASE)
                if matches:
                    job_id = matches[0]
                    # Try different URL patterns
                    potential_urls = [
                        f"{base_url}/job/{job_id}",
                        f"{base_url}/jobs/{job_id}",
                        f"{base_url}/job-detail/{job_id}",
                        f"{base_url}/vacancy/{job_id}",
                        f"{current_url}&jobId={job_id}",
                        f"{current_url}#{job_id}"
                    ]

                    for url in potential_urls:
                        try:
                            logger.debug(f"Trying programmatic URL: {url}")
                            self.driver.get(url)
                            self.random_delay(2, 3)
                            if self.driver.current_url != current_url and 'error' not in self.driver.current_url.lower():
                                logger.info(f"Programmatic navigation successful: {self.driver.current_url}")
                                return
                        except Exception:
                            continue

            # Method 2: Try to execute any JavaScript navigation functions
            navigation_scripts = [
                f"if (window.navigateToJob) window.navigateToJob({card_index});",
                f"if (window.showJobDetail) window.showJobDetail({card_index});",
                f"if (window.openJob) window.openJob({card_index});",
                "var cards = document.querySelectorAll('[data-index]'); if (cards[" + str(card_index) + "] && cards[" + str(card_index) + "].click) cards[" + str(card_index) + "].click();"
            ]

            for script in navigation_scripts:
                try:
                    self.driver.execute_script(script)
                    self.random_delay(1, 2)
                    if self.driver.current_url != current_url:
                        logger.info(f"JavaScript navigation successful: {self.driver.current_url}")
                        return
                except Exception:
                    continue

            logger.debug("Programmatic navigation failed")
            raise Exception("No programmatic navigation method worked")

        except Exception as e:
            logger.debug(f"Programmatic navigation failed: {e}")
            raise e

    def extract_job_detail_data(self, job_data: JobData) -> JobData:
        """Extract detailed job information from job detail page"""
        try:
            current_url = self.driver.current_url
            logger.info(f"Extracting job details from: {current_url}")

            # Wait for page to load - try multiple selectors
            page_loaded = False
            load_selectors = [
                '#jobDetailContainer',
                '.job-description-content',
                '#jobDescription',
                'h1',
                '[data-testid="job-title"]',
                '.job-title',
                'main',
                'article'
            ]

            for selector in load_selectors:
                try:
                    WebDriverWait(self.driver, 8).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    logger.info(f"Page loaded, found element: {selector}")
                    page_loaded = True
                    break
                except TimeoutException:
                    continue

            if not page_loaded:
                logger.warning("Job detail page did not load properly, using basic extraction")

            # Extract job title (try multiple selectors)
            title_selectors = [
                self.selectors['job_detail_title'],
                'h1',
                '[data-testid="job-title"]',
                '.job-title',
                'h2',
                '.title'
            ]

            for selector in title_selectors:
                try:
                    title_elem = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if title_elem.text.strip():
                        job_data.title = title_elem.text.strip()
                        logger.debug(f"Found title with selector {selector}: {job_data.title}")
                        break
                except NoSuchElementException:
                    continue

            # Extract company name (try multiple selectors)
            company_selectors = [
                self.selectors['job_detail_company'],
                'a[href*="jobs-career"]',
                'a[target="_blank"]',
                '.company-name',
                '[data-testid="company-name"]',
                'a[href*="company"]'
            ]

            for selector in company_selectors:
                try:
                    company_elem = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if company_elem.text.strip():
                        job_data.company_name = company_elem.text.strip()
                        logger.debug(f"Found company with selector {selector}: {job_data.company_name}")
                        break
                except NoSuchElementException:
                    continue

            # Extract job description (try multiple selectors)
            description_selectors = [
                self.selectors['job_detail_description'],
                '#jobDescription',
                '.job-description-content',
                '.job-description',
                '.description',
                '.break-words',
                '[data-testid="job-description"]'
            ]

            for selector in description_selectors:
                try:
                    desc_elem = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if desc_elem.text.strip():
                        job_data.job_description = desc_elem.text.strip()
                        logger.debug(f"Found description with selector {selector}")
                        break
                except NoSuchElementException:
                    continue

            # Extract skills (try multiple selectors)
            skill_selectors = [
                self.selectors['job_detail_skills'],
                '#skillSectionNew label',
                '.bg-surface-secondary label',
                '.skills label',
                '.skill-tag',
                '[data-testid="skills"] span'
            ]

            for selector in skill_selectors:
                try:
                    skill_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if skill_elements:
                        skills = []
                        for skill_elem in skill_elements:
                            skill_text = skill_elem.text.strip()
                            if skill_text and skill_text not in skills and len(skill_text) < 50:
                                skills.append(skill_text)
                        if skills:
                            job_data.skills = skills
                            logger.debug(f"Found {len(skills)} skills with selector {selector}")
                            break
                except NoSuchElementException:
                    continue

            # Extract job metadata (ID, posted date, etc.)
            metadata_patterns = [
                ("Job ID:", "job_id"),
                ("Date Posted:", "posted_date"),
                ("Posted:", "posted_date"),
                ("Role:", "roles"),
                ("Industry:", "industry"),
                ("Function:", "function")
            ]

            for pattern, field in metadata_patterns:
                try:
                    elem = self.driver.find_element(By.XPATH, f"//p[contains(text(), '{pattern}')]")
                    value = elem.text.replace(pattern, '').strip()
                    if value:
                        setattr(job_data, field, value)
                        logger.debug(f"Found {field}: {value}")
                except NoSuchElementException:
                    continue

            # Extract salary information
            salary_selectors = [
                "//span[contains(text(), '₹') or contains(text(), 'LPA') or contains(text(), 'salary')]",
                "//label[contains(text(), '₹') or contains(text(), 'LPA')]",
                ".salary",
                "[data-testid='salary']"
            ]

            for selector in salary_selectors:
                try:
                    if selector.startswith('//'):
                        salary_elem = self.driver.find_element(By.XPATH, selector)
                    else:
                        salary_elem = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if salary_elem.text.strip():
                        job_data.salary = salary_elem.text.strip()
                        logger.debug(f"Found salary: {job_data.salary}")
                        break
                except NoSuchElementException:
                    continue

            # Extract additional experience and location if not already present
            if not job_data.experience:
                exp_patterns = [
                    "//span[contains(text(), 'yrs') or contains(text(), 'years') or contains(text(), 'experience')]",
                    "//label[contains(text(), 'yrs') or contains(text(), 'years')]"
                ]

                for pattern in exp_patterns:
                    try:
                        exp_elem = self.driver.find_element(By.XPATH, pattern)
                        text = exp_elem.text.strip()
                        if any(keyword in text.lower() for keyword in ['year', 'yrs', 'experience']):
                            job_data.experience = text
                            logger.debug(f"Found experience: {job_data.experience}")
                            break
                    except NoSuchElementException:
                        continue

            if not job_data.location:
                location_selectors = [
                    'a[href*="jobs-in-"]',
                    "//span[contains(text(), 'Location')]",
                    ".location",
                    "[data-testid='location']"
                ]

                for selector in location_selectors:
                    try:
                        if selector.startswith('//'):
                            location_elem = self.driver.find_element(By.XPATH, selector)
                        else:
                            location_elem = self.driver.find_element(By.CSS_SELECTOR, selector)

                        if location_elem.text.strip():
                            job_data.location = location_elem.text.strip()
                            logger.debug(f"Found location: {job_data.location}")
                            break
                    except NoSuchElementException:
                        continue

            logger.info(f"Successfully extracted detailed info for: {job_data.title}")
            return job_data

        except Exception as e:
            logger.error(f"Error extracting job detail data: {e}")
            return job_data

    def go_to_next_page(self) -> bool:
        """Navigate to next page of search results"""
        try:
            # Look for next page button with various selectors
            next_selectors = [
                self.selectors['next_page'],
                'button:contains("Next")',
                '.pagination-next',
                'a[aria-label="Next"]',
                'button[aria-label="Next"]'
            ]

            for selector in next_selectors:
                try:
                    next_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if next_button.is_displayed() and next_button.is_enabled():
                        # Scroll to button
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                        self.random_delay(1, 2)

                        # Click next button
                        try:
                            next_button.click()
                        except ElementClickInterceptedException:
                            self.driver.execute_script("arguments[0].click();", next_button)

                        self.random_delay(3, 5)

                        # Wait for new page to load
                        try:
                            WebDriverWait(self.driver, 10).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards']))
                            )
                            logger.info("Navigated to next page")
                            return True
                        except TimeoutException:
                            logger.warning("Next page did not load properly")
                            return False

                except NoSuchElementException:
                    continue

            logger.info("No next page button found")
            return False

        except Exception as e:
            logger.warning(f"Error navigating to next page: {e}")
            return False

    def scrape_jobs(self, job_title: str, location: str, num_jobs: int = 5) -> Dict[str, List[Dict]]:
        """Main scraping function with direct URL navigation and click-through extraction"""
        driver = None
        jobs = []
        scraped_count = 0
        page_num = 1

        try:
            logger.info(f"Starting scrape for '{job_title}' in '{location}' - Target: {num_jobs} jobs")

            # Setup driver with retry logic
            try:
                driver = self.setup_driver()
                self.driver = driver
            except Exception as e:
                logger.error(f"Failed to setup driver: {e}")
                return {'scraped_jobs': [], 'total_scraped': 0, 'error': f"Driver setup failed: {str(e)}"}

            # Navigate directly to search results
            try:
                if not self.navigate_to_search_results(job_title, location):
                    raise Exception("Failed to navigate to search results")
                logger.info("Successfully navigated to search results page")
            except Exception as e:
                logger.error(f"Error navigating to search results: {e}")
                return {'scraped_jobs': [], 'total_scraped': 0, 'error': f"Failed to navigate to search results: {str(e)}"}

            while scraped_count < num_jobs and page_num <= 10:  # Limit to 10 pages max
                logger.info(f"Scraping page {page_num}...")

                # Extract basic job data from search results page
                page_jobs = self.extract_job_cards_data()

                if not page_jobs:
                    # Check if this is a "no results" page vs a loading issue
                    page_title = self.driver.title
                    if ': 0 ' in page_title or 'no jobs' in page_title.lower() or '0 job vacancies' in page_title.lower():
                        logger.info("Search completed successfully - no jobs found for the given criteria")
                        break
                    else:
                        logger.warning("No jobs found on current page - might be a loading issue")
                        break

                logger.info(f"Found {len(page_jobs)} jobs on page {page_num}")

                # Process each job with click-through navigation
                for i, job_data in enumerate(page_jobs):
                    if scraped_count >= num_jobs:
                        break

                    try:
                        if job_data.title:  # Only process if we got meaningful data
                            # Click through to job detail page and extract detailed information
                            detailed_job_data = self.click_through_to_job_detail(job_data, i)
                            jobs.append(asdict(detailed_job_data))

                            if '/job/' in detailed_job_data.job_url:
                                logger.info(f"Scraped detailed job {scraped_count + 1}/{num_jobs}: {detailed_job_data.title}")
                            else:
                                logger.info(f"Scraped basic job {scraped_count + 1}/{num_jobs}: {detailed_job_data.title}")

                            scraped_count += 1

                            # Add delay between job processing
                            if scraped_count < num_jobs:
                                self.random_delay(1, 2)

                    except Exception as e:
                        logger.error(f"Error processing job {i}: {e}")
                        continue

                # Try to navigate to next page
                if scraped_count < num_jobs:
                    if not self.go_to_next_page():
                        logger.info("No more pages available")
                        break
                    page_num += 1
                else:
                    break

            logger.info(f"Scraping completed. Total jobs scraped: {len(jobs)}")

            # Provide informative response based on results
            result = {'scraped_jobs': jobs, 'total_scraped': len(jobs), 'requested': num_jobs}

            if len(jobs) == 0:
                # Check if this was a "no results" case
                page_title = self.driver.title if self.driver else ""
                if ': 0 ' in page_title or 'no jobs' in page_title.lower() or '0 job vacancies' in page_title.lower():
                    result['message'] = f"No jobs found for '{job_title}' in '{location}' on Foundit. Try different search terms or location."
                    result['suggestion'] = "Try searching for 'Data Scientist' in 'Bangalore' or 'Mumbai' instead of 'India'"
                else:
                    result['message'] = "No jobs could be extracted. This might be due to page loading issues or changed website structure."

            return result

        except Exception as e:
            logger.error(f"Fatal error during scraping: {e}")
            return {'scraped_jobs': jobs, 'total_scraped': len(jobs), 'error': str(e)}

        finally:
            if driver:
                try:
                    driver.quit()
                    logger.info("Driver closed")
                except Exception as e:
                    logger.warning(f"Error closing driver: {e}")

    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Driver closed successfully")
            except Exception as e:
                logger.warning(f"Error closing driver: {e}")

# Global scraper instance
scraper = None

def get_scraper():
    """Get or create scraper instance"""
    global scraper
    if scraper is None:
        scraper = FounditScraper(headless=True)
    return scraper

@app.post("/scrape")
async def scrape_jobs_endpoint(
    job_title: str = Body(..., description="Job title to search for"),
    location: str = Body(..., description="Location to search in"),
    num_jobs: int = Body(5, description="Number of jobs to scrape")
):
    """Scrape jobs from Foundit.in"""
    try:
        logger.info(f"Received scraping request: {job_title} in {location}, {num_jobs} jobs")

        # Validate inputs
        if not job_title or not job_title.strip():
            raise HTTPException(status_code=400, detail="Job title is required")
        if not location or not location.strip():
            raise HTTPException(status_code=400, detail="Location is required")
        if num_jobs <= 0 or num_jobs > 50:
            raise HTTPException(status_code=400, detail="Number of jobs must be between 1 and 50")

        # Get scraper instance
        scraper_instance = FounditScraper(headless=True)

        # Scrape jobs
        result = scraper_instance.scrape_jobs(
            job_title=job_title.strip(),
            location=location.strip(),
            num_jobs=num_jobs
        )

        return JSONResponse(content=result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in scrape endpoint: {e}")
        return JSONResponse(
            status_code=500,
            content={
                'scraped_jobs': [],
                'total_scraped': 0,
                'error': f"Internal server error: {str(e)}"
            }
        )

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Foundit Job Scraper API v3.0", "status": "active"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "foundit-scraper"}

if __name__ == "__main__":
    import uvicorn
    logger.info("Starting Foundit Job Scraper API v3.0...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
